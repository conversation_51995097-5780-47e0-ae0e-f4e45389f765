@echo off
echo Setting up IDM-style Download Server for Windows...
echo.

REM Check if Python is installed
python --version >nul 2>&1
if errorlevel 1 (
    echo ERROR: Python is not installed or not in PATH
    echo Please install Python from https://python.org
    pause
    exit /b 1
)

REM Check if pip is available
pip --version >nul 2>&1
if errorlevel 1 (
    echo ERROR: pip is not available
    echo Please ensure pip is installed with Python
    pause
    exit /b 1
)

echo Creating virtual environment...
python -m venv venv
if errorlevel 1 (
    echo ERROR: Failed to create virtual environment
    pause
    exit /b 1
)

echo Activating virtual environment...
call venv\Scripts\activate.bat
if errorlevel 1 (
    echo ERROR: Failed to activate virtual environment
    pause
    exit /b 1
)

echo Installing dependencies...
pip install -r requirements.txt
if errorlevel 1 (
    echo ERROR: Failed to install dependencies
    pause
    exit /b 1
)

echo.
echo Starting IDM-style Download Server...
echo Server will be available at: http://localhost:8000
echo.
echo API Endpoints:
echo - Download: http://localhost:8000/download?url=YOUR_URL
echo - Download Info: http://localhost:8000/download-info?url=YOUR_URL
echo - Test Download: http://localhost:8000/test-download
echo.
echo Press Ctrl+C to stop the server
echo.

python -m uvicorn main1:app --reload --host 0.0.0.0 --port 8000

pause 