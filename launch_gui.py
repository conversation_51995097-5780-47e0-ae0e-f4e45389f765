import tkinter as tk
from tkinter import ttk, messagebox, scrolledtext
import subprocess
import threading
import sys
import os
import webbrowser
from pathlib import Path


class DownloadServerGUI:
    def __init__(self, root):
        self.root = root
        self.root.title("IDM-Style Download Server")
        self.root.geometry("600x500")
        self.root.resizable(True, True)

        # Server process
        self.server_process = None
        self.is_running = False

        self.setup_ui()

    def setup_ui(self):
        # Main frame
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky="nsew")

        # Configure grid weights
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        main_frame.rowconfigure(3, weight=1)

        # Title
        title_label = ttk.Label(main_frame, text="🚀 IDM-Style Download Server",
                                font=("Arial", 16, "bold"))
        title_label.grid(row=0, column=0, columnspan=2, pady=(0, 20))

        # Status frame
        status_frame = ttk.LabelFrame(
            main_frame, text="Server Status", padding="10")
        status_frame.grid(row=1, column=0, columnspan=2,
                          sticky="ew", pady=(0, 10))

        self.status_label = ttk.Label(status_frame, text="⏹️ Server Stopped",
                                      font=("Arial", 12))
        self.status_label.grid(row=0, column=0, sticky=tk.W)

        # Control buttons
        button_frame = ttk.Frame(main_frame)
        button_frame.grid(row=2, column=0, columnspan=2, pady=(0, 10))

        self.start_button = ttk.Button(button_frame, text="▶️ Start Server",
                                       command=self.start_server)
        self.start_button.grid(row=0, column=0, padx=(0, 10))

        self.stop_button = ttk.Button(button_frame, text="⏹️ Stop Server",
                                      command=self.stop_server, state="disabled")
        self.stop_button.grid(row=0, column=1, padx=(0, 10))

        self.open_browser_button = ttk.Button(button_frame, text="🌐 Open Browser",
                                              command=self.open_browser)
        self.open_browser_button.grid(row=0, column=2, padx=(0, 10))

        self.open_docs_button = ttk.Button(button_frame, text="📚 API Docs",
                                           command=self.open_docs)
        self.open_docs_button.grid(row=0, column=3)

        # Log frame
        log_frame = ttk.LabelFrame(main_frame, text="Server Log", padding="10")
        log_frame.grid(row=3, column=0, columnspan=2,
                       sticky="nsew")
        log_frame.columnconfigure(0, weight=1)
        log_frame.rowconfigure(0, weight=1)

        self.log_text = scrolledtext.ScrolledText(
            log_frame, height=15, width=70)
        self.log_text.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))

        # Info frame
        info_frame = ttk.LabelFrame(
            main_frame, text="Quick Info", padding="10")
        info_frame.grid(row=4, column=0, columnspan=2,
                        sticky=(tk.W, tk.E), pady=(10, 0))

        info_text = """
🌐 Server URL: http://localhost:8000
📥 Download API: /download?url=YOUR_URL
📊 Download Info: /download-info?url=YOUR_URL
🧪 Test Download: /test-download
        """

        info_label = ttk.Label(
            info_frame, text=info_text, font=("Consolas", 9))
        info_label.grid(row=0, column=0, sticky=tk.W)

    def log_message(self, message):
        self.log_text.insert(tk.END, f"{message}\n")
        self.log_text.see(tk.END)
        self.root.update_idletasks()

    def start_server(self):
        if self.is_running:
            return

        self.log_message("🚀 Starting IDM-Style Download Server...")

        # Check if virtual environment exists
        venv_path = Path("venv")
        if not venv_path.exists():
            self.log_message(
                "⚠️ Virtual environment not found. Please run setup first.")
            messagebox.showwarning("Setup Required",
                                   "Virtual environment not found.\n\n"
                                   "Please run 'run_windows.bat' first to set up the environment.")
            return

        # Start server in a separate thread
        def run_server():
            try:
                # Activate virtual environment and start server
                if sys.platform == "win32":
                    activate_script = "venv\\Scripts\\activate.bat"
                    cmd = f'call "{activate_script}" && python -m uvicorn main1:app --reload --host 0.0.0.0 --port 8000'
                else:
                    activate_script = "venv/bin/activate"
                    cmd = f'source "{activate_script}" && python -m uvicorn main1:app --reload --host 0.0.0.0 --port 8000'

                self.server_process = subprocess.Popen(
                    cmd,
                    shell=True,
                    stdout=subprocess.PIPE,
                    stderr=subprocess.STDOUT,
                    universal_newlines=True,
                    bufsize=1
                )

                # Read output
                for line in iter(self.server_process.stdout.readline, ''):
                    if line:
                        self.log_message(line.strip())

            except Exception as e:
                self.log_message(f"❌ Error starting server: {e}")
                self.is_running = False
                self.update_ui()

        # Start server thread
        server_thread = threading.Thread(target=run_server, daemon=True)
        server_thread.start()

        # Update UI
        self.is_running = True
        self.update_ui()

        # Wait a moment then check if server started
        self.root.after(2000, self.check_server_status)

    def check_server_status(self):
        if self.is_running and self.server_process:
            if self.server_process.poll() is None:
                self.log_message("✅ Server started successfully!")
                self.status_label.config(text="🟢 Server Running")
            else:
                self.log_message("❌ Server failed to start")
                self.is_running = False
                self.update_ui()

    def stop_server(self):
        if not self.is_running:
            return

        self.log_message("⏹️ Stopping server...")

        if self.server_process:
            self.server_process.terminate()
            try:
                self.server_process.wait(timeout=5)
            except subprocess.TimeoutExpired:
                self.server_process.kill()

        self.is_running = False
        self.update_ui()
        self.log_message("✅ Server stopped")

    def update_ui(self):
        if self.is_running:
            self.status_label.config(text="🟢 Server Running")
            self.start_button.config(state="disabled")
            self.stop_button.config(state="normal")
        else:
            self.status_label.config(text="⏹️ Server Stopped")
            self.start_button.config(state="normal")
            self.stop_button.config(state="disabled")

    def open_browser(self):
        webbrowser.open("http://localhost:8000")

    def open_docs(self):
        webbrowser.open("http://localhost:8000/docs")


def main():
    root = tk.Tk()
    app = DownloadServerGUI(root)

    # Handle window close
    def on_closing():
        if app.is_running:
            if messagebox.askokcancel("Quit", "Server is running. Stop server and quit?"):
                app.stop_server()
        root.destroy()

    root.protocol("WM_DELETE_WINDOW", on_closing)
    root.mainloop()


if __name__ == "__main__":
    main()
