from fastapi import FastAP<PERSON>, Query
import requests
import os
from urllib.parse import urlparse, unquote
import re
import mimetypes
import threading
import time
from concurrent.futures import Thread<PERSON>oolExecutor, as_completed
import tempfile
import shutil

app = FastAPI()
DOWNLOAD_DIR = "downloads"
os.makedirs(DOWNLOAD_DIR, exist_ok=True)

# Configuration for IDM-style downloading
CHUNK_SIZE = 1024 * 1024  # 1MB chunks
MAX_WORKERS = 8  # Number of parallel threads
MIN_SEGMENT_SIZE = 1024 * 1024  # 1MB minimum segment size
MAX_SEGMENT_SIZE = 10 * 1024 * 1024  # 10MB maximum segment size


def extract_google_drive_download_url(url):
    """Extract the actual download URL from Google Drive sharing links"""
    # Check if it's a Google Drive link
    if 'drive.google.com' in url:
        # Extract file ID from the URL
        file_id_match = re.search(r'/file/d/([a-zA-Z0-9_-]+)', url)
        if file_id_match:
            file_id = file_id_match.group(1)
            # Create direct download URL
            download_url = f"https://drive.google.com/uc?export=download&id={file_id}"
            return download_url, None
    return url, None


def get_file_size(url):
    """Get file size using HEAD request"""
    try:
        response = requests.head(url, allow_redirects=True)
        response.raise_for_status()

        # Try to get size from Content-Length header
        content_length = response.headers.get('content-length')
        if content_length:
            return int(content_length)

        # Try to get size from Content-Range header
        content_range = response.headers.get('content-range')
        if content_range:
            match = re.search(r'bytes \d+-\d+/(\d+)', content_range)
            if match:
                return int(match.group(1))

        return None
    except Exception:
        return None


def check_range_support(url):
    """Check if server supports Range requests"""
    try:
        response = requests.head(url, headers={'Range': 'bytes=0-1023'})
        return response.status_code == 206
    except Exception:
        return False


def download_segment(url, start_byte, end_byte, segment_id, temp_dir):
    """Download a specific segment of the file"""
    headers = {
        'Range': f'bytes={start_byte}-{end_byte}',
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
    }

    segment_file = os.path.join(temp_dir, f'segment_{segment_id}.tmp')
    max_retries = 3
    retry_count = 0

    while retry_count < max_retries:
        try:
            response = requests.get(
                url, headers=headers, stream=True, timeout=30)
            response.raise_for_status()

            with open(segment_file, 'wb') as f:
                for chunk in response.iter_content(chunk_size=8192):
                    if chunk:
                        f.write(chunk)

            return segment_id, True, None

        except Exception as e:
            retry_count += 1
            if retry_count >= max_retries:
                return segment_id, False, str(e)
            time.sleep(1)  # Wait before retry


def merge_segments(segment_files, output_file):
    """Merge downloaded segments into final file"""
    with open(output_file, 'wb') as outfile:
        for segment_file in sorted(segment_files):
            if os.path.exists(segment_file):
                with open(segment_file, 'rb') as infile:
                    shutil.copyfileobj(infile, outfile)


def idm_style_download(url, output_file, file_size=None):
    """IDM-style multi-threaded download with resume capability"""

    # Check if server supports Range requests
    if not check_range_support(url):
        print("Server doesn't support Range requests, falling back to single-threaded download")
        return single_threaded_download(url, output_file)

    # Get file size if not provided
    if file_size is None:
        file_size = get_file_size(url)
        if file_size is None:
            print(
                "Could not determine file size, falling back to single-threaded download")
            return single_threaded_download(url, output_file)

    # Calculate optimal segment size and number of segments
    num_segments = min(MAX_WORKERS, max(1, file_size // MAX_SEGMENT_SIZE))
    segment_size = file_size // num_segments

    # Ensure minimum segment size
    if segment_size < MIN_SEGMENT_SIZE:
        segment_size = MIN_SEGMENT_SIZE
        num_segments = max(1, file_size // segment_size)

    print(
        f"Downloading {file_size} bytes in {num_segments} segments of ~{segment_size} bytes each")

    # Create temporary directory for segments
    temp_dir = tempfile.mkdtemp()
    segment_files = []

    try:
        # Prepare segment tasks
        tasks = []
        with ThreadPoolExecutor(max_workers=MAX_WORKERS) as executor:
            for i in range(num_segments):
                start_byte = i * segment_size
                end_byte = start_byte + segment_size - 1

                # Last segment gets remaining bytes
                if i == num_segments - 1:
                    end_byte = file_size - 1

                future = executor.submit(
                    download_segment, url, start_byte, end_byte, i, temp_dir)
                tasks.append(future)

            # Wait for all segments to complete
            completed_segments = 0
            failed_segments = []

            for future in as_completed(tasks):
                segment_id, success, error = future.result()
                if success:
                    completed_segments += 1
                    segment_file = os.path.join(
                        temp_dir, f'segment_{segment_id}.tmp')
                    segment_files.append(segment_file)
                    print(f"Segment {segment_id + 1}/{num_segments} completed")
                else:
                    failed_segments.append((segment_id, error))
                    print(f"Segment {segment_id + 1} failed: {error}")

            # Check if all segments completed successfully
            if len(failed_segments) > 0:
                print(f"Failed to download {len(failed_segments)} segments")
                return False, f"Failed segments: {failed_segments}"

            # Merge segments into final file
            print("Merging segments...")
            merge_segments(segment_files, output_file)

            print(f"Download completed successfully: {output_file}")
            return True, output_file

    finally:
        # Clean up temporary files
        for segment_file in segment_files:
            if os.path.exists(segment_file):
                os.remove(segment_file)
        if os.path.exists(temp_dir):
            os.rmdir(temp_dir)


def single_threaded_download(url, output_file):
    """Fallback to single-threaded download"""
    try:
        response = requests.get(url, stream=True, timeout=30)
        response.raise_for_status()

        with open(output_file, 'wb') as f:
            for chunk in response.iter_content(chunk_size=8192):
                if chunk:
                    f.write(chunk)

        return True, output_file
    except Exception as e:
        return False, str(e)


@app.get("/download")
def download_file(url: str = Query(..., description="Direct file download URL"),
                  use_idm_style: bool = Query(True, description="Use IDM-style multi-threaded download")):
    try:
        # Handle Google Drive links
        download_url, filename = extract_google_drive_download_url(url)

        # Get initial response to check headers
        response = requests.head(download_url, allow_redirects=True)
        response.raise_for_status()

        # Step 1: Try to get filename from Content-Disposition header
        content_disposition = response.headers.get('content-disposition')
        if not filename:
            filename = None

        if content_disposition:
            # Improved regex to extract filename from Content-Disposition header
            # This handles both quoted and unquoted filenames
            fname_match = re.search(
                r'filename\*?=(?:UTF-8\'\'|")?([^";\r\n]*)', content_disposition, re.IGNORECASE)
            if fname_match:
                filename = fname_match.group(1).strip()
                # Remove quotes if present
                if filename.startswith('"') and filename.endswith('"'):
                    filename = filename[1:-1]
                # Handle URL-encoded filenames
                filename = unquote(filename)

        # Step 2: Fallback to URL-based filename if header is missing
        if not filename:
            parsed_url = urlparse(download_url)
            # Get the path and remove query parameters and fragments
            path = parsed_url.path
            # Remove query parameters and fragments from the path
            if '?' in path:
                path = path.split('?')[0]
            if '#' in path:
                path = path.split('#')[0]
            filename = os.path.basename(path)
            # URL-decode the filename
            filename = unquote(filename)

        # Step 3: If still no filename or no extension, try to determine from Content-Type
        if not filename or "." not in filename:
            content_type = response.headers.get(
                'content-type', '').split(';')[0].strip()
            if content_type and content_type != 'application/octet-stream':
                # Try to get extension from MIME type
                extension = mimetypes.guess_extension(content_type)
                if extension:
                    filename = f"downloaded_file_{str(hash(url))[:6]}{extension}"
                else:
                    filename = f"downloaded_file_{str(hash(url))[:6]}.bin"
            else:
                filename = f"downloaded_file_{str(hash(url))[:6]}.bin"

        # Ensure filename is safe for filesystem
        filename = re.sub(r'[<>:"/\\|?*]', '_', filename)

        filepath = os.path.join(DOWNLOAD_DIR, filename)

        # Choose download method
        if use_idm_style:
            print(f"Starting IDM-style download: {download_url}")
            success, result = idm_style_download(download_url, filepath)
        else:
            print(f"Starting single-threaded download: {download_url}")
            success, result = single_threaded_download(download_url, filepath)

        if success:
            return {"status": "success", "file": filepath, "method": "idm_style" if use_idm_style else "single_threaded"}
        else:
            return {"status": "error", "message": result}

    except Exception as e:
        return {"status": "error", "message": str(e)}


@app.get("/download-info")
def get_download_info(url: str = Query(..., description="URL to analyze")):
    """Get information about download capabilities"""
    try:
        download_url, _ = extract_google_drive_download_url(url)

        # Check file size
        file_size = get_file_size(download_url)

        # Check Range support
        range_support = check_range_support(download_url)

        # Get headers
        response = requests.head(download_url, allow_redirects=True)
        headers = dict(response.headers)

        return {
            "url": download_url,
            "file_size": file_size,
            "range_support": range_support,
            "content_type": headers.get('content-type'),
            "content_disposition": headers.get('content-disposition'),
            "server": headers.get('server'),
            "recommended_method": "idm_style" if range_support and file_size and file_size > 1024*1024 else "single_threaded"
        }
    except Exception as e:
        return {"status": "error", "message": str(e)}


@app.get("/test-range-server")
def test_range_server():
    """Test endpoint that simulates a Range-supporting server"""
    return {
        "message": "This is a test file for Range requests",
        "size": 1024,
        "supports_range": True
    }


@app.get("/test-download")
def test_download():
    """Test the IDM-style download with a simulated large file"""
    # Simulate downloading a large file in segments
    test_url = "https://httpbin.org/bytes/1048576"  # 1MB file

    print("Testing IDM-style download capabilities...")

    # Test single-threaded
    print("\n1. Testing single-threaded download...")
    start_time = time.time()
    success, result = single_threaded_download(
        test_url, "downloads/test_single.bin")
    single_time = time.time() - start_time

    # Test IDM-style (will fall back to single-threaded for this URL)
    print("\n2. Testing IDM-style download...")
    start_time = time.time()
    success2, result2 = idm_style_download(test_url, "downloads/test_idm.bin")
    idm_time = time.time() - start_time

    return {
        "single_threaded": {
            "success": success,
            "time": round(single_time, 2),
            "file": result if success else "error"
        },
        "idm_style": {
            "success": success2,
            "time": round(idm_time, 2),
            "file": result2 if success2 else "error"
        },
        "comparison": {
            "single_threaded_time": round(single_time, 2),
            "idm_time": round(idm_time, 2),
            "speedup": round(single_time / idm_time, 2) if idm_time > 0 else "N/A"
        }
    }
