# 🪟 Complete Windows Setup for IDM-Style Download Server

## 📦 What You Get

Your Windows-compatible IDM-style download server includes:

### 🚀 **4 Ways to Run:**

1. **One-Click Batch File** (`run_windows.bat`) - **Easiest**
2. **PowerShell Script** (`run_windows_powershell.ps1`) - **Modern**
3. **GUI Launcher** (`launch_gui.py`) - **Visual**
4. **Manual Setup** - **Advanced**

### 📁 **Complete File Structure:**

```
📁 Your Project Folder/
├── 🐍 main1.py                           # Main server with IDM features
├── 📋 requirements.txt                    # Python dependencies
├── ⚡ run_windows.bat                     # One-click setup (CMD)
├── 🔧 run_windows_powershell.ps1         # One-click setup (PowerShell)
├── 🖥️ launch_gui.py                      # GUI launcher
├── 📖 README_Windows.md                   # Detailed setup guide
├── 📖 Windows_Setup_Complete.md           # This guide
└── 📁 downloads/                          # Downloaded files folder
```

## 🚀 **Quick Start (Choose Your Method)**

### **Method 1: One-Click Batch File (Recommended)**

```cmd
# Just double-click this file:
run_windows.bat
```

### **Method 2: PowerShell Script**

```powershell
# Right-click and "Run with PowerShell":
run_windows_powershell.ps1
```

### **Method 3: GUI Launcher**

```cmd
# After setup, run:
python launch_gui.py
```

### **Method 4: Manual Setup**

```cmd
# Step by step:
python -m venv venv
venv\Scripts\activate.bat
pip install -r requirements.txt
python -m uvicorn main1:app --reload --host 0.0.0.0 --port 8000
```

## ⚡ **IDM-Style Features Included:**

### ✅ **Multi-threaded Downloads**

- Splits files into segments (1MB-10MB each)
- Downloads 8 segments simultaneously
- Uses multiple TCP connections

### ✅ **Range Request Support**

- Automatically detects server capabilities
- Uses `Range: bytes=X-Y` headers
- Falls back to single-threaded if not supported

### ✅ **Auto-Resume Capability**

- 3 retry attempts per segment
- Continues from failed segments
- Handles network interruptions

### ✅ **Google Drive Integration**

- Handles Google Drive sharing links
- Extracts direct download URLs
- Preserves original filenames

### ✅ **Smart Fallback System**

- Detects server capabilities
- Chooses optimal download method
- Maintains compatibility

## 🌐 **API Endpoints Available:**

### **1. Download Files**

```cmd
curl "http://localhost:8000/download?url=YOUR_FILE_URL"
```

### **2. Check Download Capabilities**

```cmd
curl "http://localhost:8000/download-info?url=YOUR_FILE_URL"
```

### **3. Test Performance**

```cmd
curl "http://localhost:8000/test-download"
```

### **4. Web Interface**

```
http://localhost:8000/docs
```

## 🔧 **Prerequisites:**

### **Required:**

- ✅ Python 3.8+ installed
- ✅ Python added to PATH
- ✅ Internet connection

### **Optional:**

- ✅ curl (for command line testing)
- ✅ Git (for version control)

## 🛠️ **Troubleshooting:**

### **Common Issues & Solutions:**

#### **1. "Python is not recognized"**

```cmd
# Solution: Reinstall Python with "Add to PATH" checked
# Or manually add to PATH in Environment Variables
```

#### **2. "pip is not recognized"**

```cmd
python -m ensurepip --upgrade
```

#### **3. PowerShell Execution Policy Error**

```powershell
Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope CurrentUser
```

#### **4. Port 8000 Already in Use**

```cmd
# Change port:
python -m uvicorn main1:app --port 8001

# Or kill process:
netstat -ano | findstr :8000
taskkill /PID <PID> /F
```

#### **5. Virtual Environment Issues**

```cmd
# Delete and recreate:
rmdir /s venv
python -m venv venv
venv\Scripts\activate.bat
pip install -r requirements.txt
```

## 📊 **Performance Comparison:**

| Method              | Speed  | Reliability | Features |
| ------------------- | ------ | ----------- | -------- |
| **IDM-Style**       | ⚡⚡⚡ | ⭐⭐⭐      | 🎯 Full  |
| **Single-Threaded** | ⚡     | ⭐⭐⭐⭐⭐  | 🎯 Basic |

## 🔒 **Security Notes:**

- Server runs on `0.0.0.0:8000` (network accessible)
- For local-only: change to `--host 127.0.0.1`
- Downloads saved to `downloads/` folder
- Temporary files auto-cleaned

## 📞 **Support:**

### **If Something Goes Wrong:**

1. Check Python: `python --version`
2. Check pip: `pip --version`
3. Verify venv: Should see `(venv)` in prompt
4. Check firewall settings
5. Try different port if 8000 is busy

### **For Advanced Users:**

- Edit `main1.py` to customize settings
- Modify `MAX_WORKERS` for more/less threads
- Adjust `CHUNK_SIZE` for different segment sizes
- Add authentication if needed

## 🎉 **You're Ready!**

Your Windows machine now has **IDM-level download capabilities** with:

- ✅ Multi-threaded downloads
- ✅ Auto-resume functionality
- ✅ Google Drive support
- ✅ Smart fallback system
- ✅ Easy setup scripts
- ✅ GUI launcher option

**Start downloading faster than ever!** 🚀
