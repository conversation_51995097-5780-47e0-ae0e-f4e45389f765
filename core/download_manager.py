"""
Download manager for handling multiple concurrent downloads
"""

import os
import threading
import json
from typing import List, Dict, Optional, Callable, Any
from queue import Queue, Empty
from concurrent.futures import Thread<PERSON>oolExecutor, Future
import uuid

from .download_engine import DownloadEngine, DownloadStatus, ProgressInfo
from .download_item import DownloadItem


class DownloadManager:
    """
    Manages multiple downloads with queue, progress tracking, and persistence
    """

    def __init__(self,
                 download_dir: str = "downloads",
                 max_concurrent_downloads: int = 3,
                 max_workers_per_download: int = 8):
        self.download_dir = download_dir
        self.max_concurrent_downloads = max_concurrent_downloads
        self.max_workers_per_download = max_workers_per_download

        # Ensure download directory exists
        os.makedirs(self.download_dir, exist_ok=True)

        # Download tracking
        self.downloads: Dict[str, DownloadItem] = {}
        self.download_queue: Queue = Queue()
        self.active_downloads: Dict[str, Future] = {}

        # Threading
        self.executor = ThreadPoolExecutor(
            max_workers=max_concurrent_downloads)
        self._shutdown = False
        self._lock = threading.RLock()

        # Callbacks
        self.progress_callbacks: List[Callable[[str, ProgressInfo], None]] = []
        self.status_callbacks: List[Callable[[str, DownloadStatus], None]] = []

        # Start queue processor
        self._queue_thread = threading.Thread(
            target=self._process_queue, daemon=True)
        self._queue_thread.start()

    def add_download(self, url: str, filename: Optional[str] = None,
                     output_path: Optional[str] = None) -> str:
        """
        Add a new download to the queue
        Returns: download_id
        """
        download_id = str(uuid.uuid4())

        # Create download engine to get filename if not provided
        engine = DownloadEngine(max_workers=self.max_workers_per_download)

        if filename is None:
            filename = engine.get_filename_from_url(url)

        if output_path is None:
            output_path = self.download_dir

        # Create download item
        download_item = DownloadItem(
            url=url,
            filename=filename,
            output_path=output_path
        )
        download_item._engine = engine

        with self._lock:
            self.downloads[download_id] = download_item
            self.download_queue.put(download_id)

        self._notify_status_change(download_id, DownloadStatus.PENDING)
        return download_id

    def start_download(self, download_id: str) -> bool:
        """Start a specific download immediately"""
        with self._lock:
            if download_id not in self.downloads:
                return False

            download_item = self.downloads[download_id]
            if download_item.status != DownloadStatus.PENDING:
                return False

            # Add to front of queue for immediate processing
            temp_queue = Queue()
            temp_queue.put(download_id)

            # Move existing items back
            while not self.download_queue.empty():
                try:
                    item = self.download_queue.get_nowait()
                    if item != download_id:  # Avoid duplicates
                        temp_queue.put(item)
                except Empty:
                    break

            self.download_queue = temp_queue
            return True

    def pause_download(self, download_id: str) -> bool:
        """Pause a download"""
        with self._lock:
            if download_id not in self.downloads:
                return False

            download_item = self.downloads[download_id]
            if download_item.status == DownloadStatus.DOWNLOADING:
                if download_item._engine:
                    download_item._engine.pause()
                download_item.status = DownloadStatus.PAUSED
                self._notify_status_change(download_id, DownloadStatus.PAUSED)
                return True
            return False

    def resume_download(self, download_id: str) -> bool:
        """Resume a paused download"""
        with self._lock:
            if download_id not in self.downloads:
                return False

            download_item = self.downloads[download_id]
            if download_item.status == DownloadStatus.PAUSED:
                if download_item._engine:
                    download_item._engine.resume()
                download_item.status = DownloadStatus.PENDING
                self.download_queue.put(download_id)
                self._notify_status_change(download_id, DownloadStatus.PENDING)
                return True
            return False

    def cancel_download(self, download_id: str) -> bool:
        """Cancel a download"""
        with self._lock:
            if download_id not in self.downloads:
                return False

            download_item = self.downloads[download_id]

            # Cancel if currently downloading
            if download_item.status == DownloadStatus.DOWNLOADING:
                if download_item._engine:
                    download_item._engine.cancel()

                # Cancel future if active
                if download_id in self.active_downloads:
                    future = self.active_downloads[download_id]
                    future.cancel()
                    del self.active_downloads[download_id]

            download_item.status = DownloadStatus.CANCELLED
            self._notify_status_change(download_id, DownloadStatus.CANCELLED)
            return True

    def retry_download(self, download_id: str) -> bool:
        """Retry a failed download"""
        with self._lock:
            if download_id not in self.downloads:
                return False

            download_item = self.downloads[download_id]
            if download_item.can_retry():
                download_item.increment_retry()
                download_item.reset_progress()
                self.download_queue.put(download_id)
                self._notify_status_change(download_id, DownloadStatus.PENDING)
                return True
            return False

    def remove_download(self, download_id: str, delete_file: bool = False) -> bool:
        """Remove a download from the manager"""
        with self._lock:
            if download_id not in self.downloads:
                return False

            download_item = self.downloads[download_id]

            # Cancel if active
            self.cancel_download(download_id)

            # Delete file if requested
            if delete_file and download_item.file_exists():
                try:
                    os.remove(download_item.get_file_path())
                except OSError:
                    pass

            del self.downloads[download_id]
            return True

    def get_download(self, download_id: str) -> Optional[DownloadItem]:
        """Get download item by ID"""
        with self._lock:
            return self.downloads.get(download_id)

    def get_all_downloads(self) -> List[DownloadItem]:
        """Get all downloads"""
        with self._lock:
            return list(self.downloads.values())

    def get_downloads_by_status(self, status: DownloadStatus) -> List[DownloadItem]:
        """Get downloads by status"""
        with self._lock:
            return [item for item in self.downloads.values() if item.status == status]

    def add_progress_callback(self, callback: Callable[[str, ProgressInfo], None]):
        """Add progress update callback"""
        self.progress_callbacks.append(callback)

    def add_status_callback(self, callback: Callable[[str, DownloadStatus], None]):
        """Add status change callback"""
        self.status_callbacks.append(callback)

    def _process_queue(self):
        """Process download queue in background thread"""
        while not self._shutdown:
            try:
                # Check if we can start more downloads
                with self._lock:
                    active_count = len(self.active_downloads)
                    if active_count >= self.max_concurrent_downloads:
                        import time
                        time.sleep(1)  # Wait before checking again
                        continue

                # Get next download from queue
                try:
                    download_id = self.download_queue.get(timeout=1)
                except Empty:
                    continue

                with self._lock:
                    if download_id not in self.downloads:
                        continue

                    download_item = self.downloads[download_id]
                    if download_item.status != DownloadStatus.PENDING:
                        continue

                    # Start download
                    future = self.executor.submit(
                        self._execute_download, download_id)
                    self.active_downloads[download_id] = future

            except Exception as e:
                print(f"Error in queue processor: {e}")

    def _execute_download(self, download_id: str) -> None:
        """Execute a single download"""
        try:
            with self._lock:
                download_item = self.downloads[download_id]
                engine = download_item._engine

            # Create progress callback
            def progress_callback(progress_info: ProgressInfo):
                with self._lock:
                    if download_id in self.downloads:
                        download_item.update_from_progress_info(progress_info)
                        self._notify_progress_update(
                            download_id, progress_info)

            # Start download
            download_item.status = DownloadStatus.DOWNLOADING
            self._notify_status_change(download_id, DownloadStatus.DOWNLOADING)

            success, result = engine.download(
                download_item.url,
                download_item.get_file_path(),
                progress_callback
            )

            # Update final status
            with self._lock:
                if success:
                    download_item.status = DownloadStatus.COMPLETED
                    self._notify_status_change(
                        download_id, DownloadStatus.COMPLETED)
                else:
                    download_item.set_error(result)
                    self._notify_status_change(
                        download_id, DownloadStatus.FAILED)

        except Exception as e:
            with self._lock:
                if download_id in self.downloads:
                    download_item = self.downloads[download_id]
                    download_item.set_error(str(e))
                    self._notify_status_change(
                        download_id, DownloadStatus.FAILED)

        finally:
            # Remove from active downloads
            with self._lock:
                if download_id in self.active_downloads:
                    del self.active_downloads[download_id]

    def _notify_progress_update(self, download_id: str, progress_info: ProgressInfo):
        """Notify progress callbacks"""
        for callback in self.progress_callbacks:
            try:
                callback(download_id, progress_info)
            except Exception as e:
                print(f"Error in progress callback: {e}")

    def _notify_status_change(self, download_id: str, status: DownloadStatus):
        """Notify status change callbacks"""
        for callback in self.status_callbacks:
            try:
                callback(download_id, status)
            except Exception as e:
                print(f"Error in status callback: {e}")

    def save_state(self, filepath: str = "downloads_state.json"):
        """Save download state to file"""
        with self._lock:
            state = {
                'downloads': {
                    download_id: item.to_dict()
                    for download_id, item in self.downloads.items()
                }
            }

            with open(filepath, 'w') as f:
                json.dump(state, f, indent=2)

    def load_state(self, filepath: str = "downloads_state.json"):
        """Load download state from file"""
        if not os.path.exists(filepath):
            return

        try:
            with open(filepath, 'r') as f:
                state = json.load(f)

            with self._lock:
                for download_id, item_data in state.get('downloads', {}).items():
                    download_item = DownloadItem.from_dict(item_data)
                    download_item._engine = DownloadEngine(
                        max_workers=self.max_workers_per_download)
                    self.downloads[download_id] = download_item

                    # Re-queue pending downloads
                    if download_item.status == DownloadStatus.PENDING:
                        self.download_queue.put(download_id)

        except Exception as e:
            print(f"Error loading state: {e}")

    def shutdown(self):
        """Shutdown the download manager"""
        self._shutdown = True

        # Cancel all active downloads
        with self._lock:
            for download_id in list(self.active_downloads.keys()):
                self.cancel_download(download_id)

        # Shutdown executor
        self.executor.shutdown(wait=True)

    def get_stats(self) -> Dict[str, Any]:
        """Get download statistics"""
        with self._lock:
            stats = {
                'total_downloads': len(self.downloads),
                'active_downloads': len(self.active_downloads),
                'completed_downloads': len(self.get_downloads_by_status(DownloadStatus.COMPLETED)),
                'failed_downloads': len(self.get_downloads_by_status(DownloadStatus.FAILED)),
                'pending_downloads': len(self.get_downloads_by_status(DownloadStatus.PENDING)),
                'total_downloaded_bytes': sum(item.downloaded_bytes for item in self.downloads.values()),
                'total_size_bytes': sum(item.total_bytes for item in self.downloads.values())
            }
            return stats
