# The PEP 484 type hints stub file for the QtHelp module.
#
# Generated by SIP 6.12.0
#
# <AUTHOR> <EMAIL>
# 
# This file is part of PyQt6.
# 
# This file may be used under the terms of the GNU General Public License
# version 3.0 as published by the Free Software Foundation and appearing in
# the file L<PERSON>EN<PERSON> included in the packaging of this file.  Please review the
# following information to ensure the GNU General Public License version 3.0
# requirements will be met: http://www.gnu.org/copyleft/gpl.html.
# 
# If you do not wish to use this file under the terms of the GPL version 3.0
# then you may purchase a commercial license.  For more information contact
# <EMAIL>.
# 
# This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
# WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


import collections, re, typing, enum

try:
    from warnings import deprecated
except ImportError:
    pass

import PyQt6.sip

from PyQt6 import QtCore
from PyQt6 import QtGui
from PyQt6 import QtWidgets

# Support for QDate, QDateTime and QTime.
import datetime

# Convenient type aliases.
PYQT_SIGNAL = typing.Union[QtCore.pyqtSignal, QtCore.pyqtBoundSignal]
PYQT_SLOT = typing.Union[collections.abc.Callable[..., Any], QtCore.pyqtBoundSignal]


class QCompressedHelpInfo(PyQt6.sip.simplewrapper):

    @typing.overload
    def __init__(self) -> None: ...
    @typing.overload
    def __init__(self, other: 'QCompressedHelpInfo') -> None: ...

    def isNull(self) -> bool: ...
    @staticmethod
    def fromCompressedHelpFile(documentationFileName: typing.Optional[str]) -> 'QCompressedHelpInfo': ...
    def version(self) -> QtCore.QVersionNumber: ...
    def component(self) -> str: ...
    def namespaceName(self) -> str: ...
    def swap(self, other: 'QCompressedHelpInfo') -> None: ...


class QHelpContentItem(PyQt6.sip.simplewrapper):

    def childPosition(self, child: typing.Optional['QHelpContentItem']) -> int: ...
    def parent(self) -> typing.Optional['QHelpContentItem']: ...
    def row(self) -> int: ...
    def url(self) -> QtCore.QUrl: ...
    def title(self) -> str: ...
    def childCount(self) -> int: ...
    def child(self, row: int) -> typing.Optional['QHelpContentItem']: ...


class QHelpContentModel(QtCore.QAbstractItemModel):

    contentsCreated: typing.ClassVar[QtCore.pyqtSignal]
    contentsCreationStarted: typing.ClassVar[QtCore.pyqtSignal]
    def isCreatingContents(self) -> bool: ...
    def columnCount(self, parent: QtCore.QModelIndex = ...) -> int: ...
    def rowCount(self, parent: QtCore.QModelIndex = ...) -> int: ...
    def parent(self, index: QtCore.QModelIndex) -> QtCore.QModelIndex: ...
    def index(self, row: int, column: int, parent: QtCore.QModelIndex = ...) -> QtCore.QModelIndex: ...
    def data(self, index: QtCore.QModelIndex, role: int) -> typing.Any: ...
    def contentItemAt(self, index: QtCore.QModelIndex) -> typing.Optional[QHelpContentItem]: ...
    def createContentsForCurrentFilter(self) -> None: ...
    def createContents(self, customFilterName: typing.Optional[str]) -> None: ...


class QHelpContentWidget(QtWidgets.QTreeView):

    linkActivated: typing.ClassVar[QtCore.pyqtSignal]
    def indexOf(self, link: QtCore.QUrl) -> QtCore.QModelIndex: ...


class QHelpEngineCore(QtCore.QObject):

    def __init__(self, collectionFile: typing.Optional[str], parent: typing.Optional[QtCore.QObject] = ...) -> None: ...

    def setReadOnly(self, enable: bool) -> None: ...
    def isReadOnly(self) -> bool: ...
    @typing.overload
    def documentsForKeyword(self, keyword: typing.Optional[str]) -> list['QHelpLink']: ...
    @typing.overload
    def documentsForKeyword(self, keyword: typing.Optional[str], filterName: typing.Optional[str]) -> list['QHelpLink']: ...
    @typing.overload
    def documentsForIdentifier(self, id: typing.Optional[str]) -> list['QHelpLink']: ...
    @typing.overload
    def documentsForIdentifier(self, id: typing.Optional[str], filterName: typing.Optional[str]) -> list['QHelpLink']: ...
    def usesFilterEngine(self) -> bool: ...
    def setUsesFilterEngine(self, uses: bool) -> None: ...
    def files(self, namespaceName: typing.Optional[str], filterName: typing.Optional[str], extensionFilter: typing.Optional[str] = ...) -> list[QtCore.QUrl]: ...
    def filterEngine(self) -> typing.Optional['QHelpFilterEngine']: ...
    warning: typing.ClassVar[QtCore.pyqtSignal]
    setupFinished: typing.ClassVar[QtCore.pyqtSignal]
    setupStarted: typing.ClassVar[QtCore.pyqtSignal]
    def setAutoSaveFilter(self, save: bool) -> None: ...
    def autoSaveFilter(self) -> bool: ...
    def error(self) -> str: ...
    @staticmethod
    def metaData(documentationFileName: typing.Optional[str], name: typing.Optional[str]) -> typing.Any: ...
    def setCustomValue(self, key: typing.Optional[str], value: typing.Any) -> bool: ...
    def customValue(self, key: typing.Optional[str], defaultValue: typing.Any = ...) -> typing.Any: ...
    def removeCustomValue(self, key: typing.Optional[str]) -> bool: ...
    def fileData(self, url: QtCore.QUrl) -> QtCore.QByteArray: ...
    def findFile(self, url: QtCore.QUrl) -> QtCore.QUrl: ...
    def registeredDocumentations(self) -> list[str]: ...
    def documentationFileName(self, namespaceName: typing.Optional[str]) -> str: ...
    def unregisterDocumentation(self, namespaceName: typing.Optional[str]) -> bool: ...
    def registerDocumentation(self, documentationFileName: typing.Optional[str]) -> bool: ...
    @staticmethod
    def namespaceName(documentationFileName: typing.Optional[str]) -> str: ...
    def copyCollectionFile(self, fileName: typing.Optional[str]) -> bool: ...
    def setCollectionFile(self, fileName: typing.Optional[str]) -> None: ...
    def collectionFile(self) -> str: ...
    def setupData(self) -> bool: ...


class QHelpEngine(QHelpEngineCore):

    def __init__(self, collectionFile: typing.Optional[str], parent: typing.Optional[QtCore.QObject] = ...) -> None: ...

    def searchEngine(self) -> typing.Optional['QHelpSearchEngine']: ...
    def indexWidget(self) -> typing.Optional['QHelpIndexWidget']: ...
    def contentWidget(self) -> typing.Optional[QHelpContentWidget]: ...
    def indexModel(self) -> typing.Optional['QHelpIndexModel']: ...
    def contentModel(self) -> typing.Optional[QHelpContentModel]: ...


class QHelpFilterData(PyQt6.sip.simplewrapper):

    @typing.overload
    def __init__(self) -> None: ...
    @typing.overload
    def __init__(self, other: 'QHelpFilterData') -> None: ...

    def __ne__(self, other: object): ...
    def versions(self) -> list[QtCore.QVersionNumber]: ...
    def components(self) -> list[str]: ...
    def setVersions(self, versions: collections.abc.Iterable[QtCore.QVersionNumber]) -> None: ...
    def setComponents(self, components: collections.abc.Iterable[typing.Optional[str]]) -> None: ...
    def swap(self, other: 'QHelpFilterData') -> None: ...
    def __eq__(self, other: object): ...


class QHelpFilterEngine(QtCore.QObject):

    @typing.overload
    def indices(self) -> list[str]: ...
    @typing.overload
    def indices(self, filterName: typing.Optional[str]) -> list[str]: ...
    def availableVersions(self) -> list[QtCore.QVersionNumber]: ...
    filterActivated: typing.ClassVar[QtCore.pyqtSignal]
    def namespacesForFilter(self, filterName: typing.Optional[str]) -> list[str]: ...
    def removeFilter(self, filterName: typing.Optional[str]) -> bool: ...
    def setFilterData(self, filterName: typing.Optional[str], filterData: QHelpFilterData) -> bool: ...
    def filterData(self, filterName: typing.Optional[str]) -> QHelpFilterData: ...
    def availableComponents(self) -> list[str]: ...
    def setActiveFilter(self, filterName: typing.Optional[str]) -> bool: ...
    def activeFilter(self) -> str: ...
    def filters(self) -> list[str]: ...
    def namespaceToVersion(self) -> dict[str, QtCore.QVersionNumber]: ...
    def namespaceToComponent(self) -> dict[str, str]: ...


class QHelpFilterSettingsWidget(QtWidgets.QWidget):

    def __init__(self, parent: typing.Optional[QtWidgets.QWidget] = ...) -> None: ...

    def applySettings(self, filterEngine: typing.Optional[QHelpFilterEngine]) -> bool: ...
    def readSettings(self, filterEngine: typing.Optional[QHelpFilterEngine]) -> None: ...
    def setAvailableVersions(self, versions: collections.abc.Iterable[QtCore.QVersionNumber]) -> None: ...
    def setAvailableComponents(self, components: collections.abc.Iterable[typing.Optional[str]]) -> None: ...


class QHelpIndexModel(QtCore.QStringListModel):

    indexCreated: typing.ClassVar[QtCore.pyqtSignal]
    indexCreationStarted: typing.ClassVar[QtCore.pyqtSignal]
    def isCreatingIndex(self) -> bool: ...
    def filter(self, filter: typing.Optional[str], wildcard: typing.Optional[str] = ...) -> QtCore.QModelIndex: ...
    def createIndexForCurrentFilter(self) -> None: ...
    def createIndex(self, customFilterName: typing.Optional[str]) -> None: ...
    def helpEngine(self) -> typing.Optional[QHelpEngineCore]: ...


class QHelpIndexWidget(QtWidgets.QListView):

    documentsActivated: typing.ClassVar[QtCore.pyqtSignal]
    documentActivated: typing.ClassVar[QtCore.pyqtSignal]
    def activateCurrentItem(self) -> None: ...
    def filterIndices(self, filter: typing.Optional[str], wildcard: typing.Optional[str] = ...) -> None: ...


class QHelpLink(PyQt6.sip.simplewrapper):

    title = ... # type: typing.Optional[str]
    url = ... # type: QtCore.QUrl

    @typing.overload
    def __init__(self) -> None: ...
    @typing.overload
    def __init__(self, a0: 'QHelpLink') -> None: ...


class QHelpSearchQuery(PyQt6.sip.simplewrapper):

    class FieldName(enum.Enum):
        DEFAULT = ... # type: QHelpSearchQuery.FieldName
        FUZZY = ... # type: QHelpSearchQuery.FieldName
        WITHOUT = ... # type: QHelpSearchQuery.FieldName
        PHRASE = ... # type: QHelpSearchQuery.FieldName
        ALL = ... # type: QHelpSearchQuery.FieldName
        ATLEAST = ... # type: QHelpSearchQuery.FieldName

    @typing.overload
    def __init__(self) -> None: ...
    @typing.overload
    def __init__(self, field: 'QHelpSearchQuery.FieldName', wordList: collections.abc.Iterable[typing.Optional[str]]) -> None: ...
    @typing.overload
    def __init__(self, a0: 'QHelpSearchQuery') -> None: ...


class QHelpSearchEngine(QtCore.QObject):

    def __init__(self, helpEngine: typing.Optional[QHelpEngineCore], parent: typing.Optional[QtCore.QObject] = ...) -> None: ...

    def search(self, searchInput: typing.Optional[str]) -> None: ...
    def searchInput(self) -> str: ...
    def searchResults(self, start: int, end: int) -> list['QHelpSearchResult']: ...
    def searchResultCount(self) -> int: ...
    searchingFinished: typing.ClassVar[QtCore.pyqtSignal]
    searchingStarted: typing.ClassVar[QtCore.pyqtSignal]
    indexingFinished: typing.ClassVar[QtCore.pyqtSignal]
    indexingStarted: typing.ClassVar[QtCore.pyqtSignal]
    def cancelSearching(self) -> None: ...
    def cancelIndexing(self) -> None: ...
    def reindexDocumentation(self) -> None: ...
    def resultWidget(self) -> typing.Optional['QHelpSearchResultWidget']: ...
    def queryWidget(self) -> typing.Optional['QHelpSearchQueryWidget']: ...


class QHelpSearchEngineCore(QtCore.QObject):

    def __init__(self, helpEngine: typing.Optional[QHelpEngineCore], parent: typing.Optional[QtCore.QObject] = ...) -> None: ...

    searchingFinished: typing.ClassVar[QtCore.pyqtSignal]
    searchingStarted: typing.ClassVar[QtCore.pyqtSignal]
    indexingFinished: typing.ClassVar[QtCore.pyqtSignal]
    indexingStarted: typing.ClassVar[QtCore.pyqtSignal]
    def cancelSearching(self) -> None: ...
    def search(self, searchInput: typing.Optional[str]) -> None: ...
    def cancelIndexing(self) -> None: ...
    def reindexDocumentation(self) -> None: ...
    def searchInput(self) -> str: ...
    def searchResults(self, start: int, end: int) -> list['QHelpSearchResult']: ...
    def searchResultCount(self) -> int: ...


class QHelpSearchQueryWidget(QtWidgets.QWidget):

    def __init__(self, parent: typing.Optional[QtWidgets.QWidget] = ...) -> None: ...

    def setSearchInput(self, searchInput: typing.Optional[str]) -> None: ...
    def searchInput(self) -> str: ...
    def setCompactMode(self, on: bool) -> None: ...
    def isCompactMode(self) -> bool: ...
    search: typing.ClassVar[QtCore.pyqtSignal]
    def collapseExtendedSearch(self) -> None: ...
    def expandExtendedSearch(self) -> None: ...


class QHelpSearchResult(PyQt6.sip.simplewrapper):

    @typing.overload
    def __init__(self) -> None: ...
    @typing.overload
    def __init__(self, other: 'QHelpSearchResult') -> None: ...
    @typing.overload
    def __init__(self, url: QtCore.QUrl, title: typing.Optional[str], snippet: typing.Optional[str]) -> None: ...

    def snippet(self) -> str: ...
    def url(self) -> QtCore.QUrl: ...
    def title(self) -> str: ...


class QHelpSearchResultWidget(QtWidgets.QWidget):

    requestShowLink: typing.ClassVar[QtCore.pyqtSignal]
    def linkAt(self, point: QtCore.QPoint) -> QtCore.QUrl: ...
