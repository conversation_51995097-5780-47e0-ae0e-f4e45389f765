// qmediatimerange.sip generated by MetaSIP
//
// This file is part of the QtMultimedia Python extension module.
//
// Copyright (c) 2025 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt6.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


%If (Qt_6_2_0 -)

class QMediaTimeRange
{
%TypeHeaderCode
#include <qmediatimerange.h>
%End

public:
    QMediaTimeRange();
    QMediaTimeRange(const QMediaTimeRange::Interval &);
    QMediaTimeRange(qint64 start, qint64 end);
    QMediaTimeRange(const QMediaTimeRange &range);
    ~QMediaTimeRange();
    qint64 earliestTime() const;
    qint64 latestTime() const;
    QList<QMediaTimeRange::Interval> intervals() const;
    bool isEmpty() const;
    bool isContinuous() const;
    bool contains(qint64 time) const;
    void addInterval(const QMediaTimeRange::Interval &interval);
    void addInterval(qint64 start, qint64 end);
    void addTimeRange(const QMediaTimeRange &);
    void removeInterval(const QMediaTimeRange::Interval &interval);
    void removeInterval(qint64 start, qint64 end);
    void removeTimeRange(const QMediaTimeRange &);
    QMediaTimeRange &operator+=(const QMediaTimeRange::Interval &);
    QMediaTimeRange &operator+=(const QMediaTimeRange &);
    QMediaTimeRange &operator-=(const QMediaTimeRange::Interval &);
    QMediaTimeRange &operator-=(const QMediaTimeRange &);
    void clear();

    struct Interval
    {
%TypeHeaderCode
#include <qmediatimerange.h>
%End

        Interval(qint64 start, qint64 end);
        qint64 start() const;
        qint64 end() const;
        bool contains(qint64 time) const;
        bool isNormal() const;
        QMediaTimeRange::Interval normalized() const;
        QMediaTimeRange::Interval translated(qint64 offset) const;
    };
};

%End
%If (Qt_6_2_0 -)
bool operator==(const QMediaTimeRange &, const QMediaTimeRange &);
%End
%If (Qt_6_2_0 -)
bool operator!=(const QMediaTimeRange &, const QMediaTimeRange &);
%End
%If (Qt_6_2_0 -)
QMediaTimeRange operator+(const QMediaTimeRange &, const QMediaTimeRange &);
%End
%If (Qt_6_2_0 -)
QMediaTimeRange operator-(const QMediaTimeRange &, const QMediaTimeRange &);
%End
%If (Qt_6_2_0 -)
bool operator==(QMediaTimeRange::Interval lhs, QMediaTimeRange::Interval rhs);
%End
%If (Qt_6_2_0 -)
bool operator!=(QMediaTimeRange::Interval lhs, QMediaTimeRange::Interval rhs);
%End
