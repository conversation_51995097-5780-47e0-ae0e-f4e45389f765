// qdbusabstractinterface.sip generated by MetaSIP
//
// This file is part of the QtDBus Python extension module.
//
// Copyright (c) 2025 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt6.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QDBusAbstractInterface : QObject
{
%TypeHeaderCode
#include <qdbusabstractinterface.h>
%End

%TypeCode
// Convert a Python tuple to a list of QVarients.
static QList<QVariant> QtDBus_marshal(PyObject *py_args, int *is_err)
{
    QList<QVariant> cpp_args;
    Py_ssize_t nr_args = PyTuple_Size(py_args);

    for (Py_ssize_t i = 0; i < nr_args; ++i)
    {
        int state;
        void *var;

        var = sipForceConvertToType(PyTuple_GetItem(py_args, i), sipType_QVariant, NULL,
                0, &state, is_err);

        if (*is_err)
            break;

        cpp_args.append(*reinterpret_cast<QVariant *>(var));
        sipReleaseType(var, sipType_QVariant, state);
    }

    return cpp_args;
}
%End

%ConvertToSubClassCode
    static struct class_graph {
        const char *name;
        sipTypeDef **type;
        int yes, no;
    } graph[] = {
        {sipName_QDBusPendingCallWatcher, &sipType_QDBusPendingCallWatcher, -1, 1},
        {sipName_QDBusAbstractAdaptor, &sipType_QDBusAbstractAdaptor, -1, 2},
        {sipName_QDBusAbstractInterface, &sipType_QDBusAbstractInterface, 4, 3},
        {sipName_QDBusServiceWatcher, &sipType_QDBusServiceWatcher, -1, -1},
        {sipName_QDBusConnectionInterface, &sipType_QDBusConnectionInterface, -1, 5},
        {sipName_QDBusInterface, &sipType_QDBusInterface, -1, -1},
    };
    
    int i = 0;
    
    sipType = NULL;
    
    do
    {
        struct class_graph *cg = &graph[i];
    
        if (cg->name != NULL && sipCpp->inherits(cg->name))
        {
            sipType = *cg->type;
            i = cg->yes;
        }
        else
            i = cg->no;
    }
    while (i >= 0);
%End

public:
    virtual ~QDBusAbstractInterface();
    bool isValid() const;
    QDBusConnection connection() const;
    QString service() const;
    QString path() const;
    QString interface() const;
    QDBusError lastError() const;
    void setTimeout(int timeout);
    int timeout() const;
    QDBusMessage call(const QString &method, ...);
%MethodCode
        QList<QVariant> args;
        
        args = QtDBus_marshal(a1, &sipIsErr);
        
        if (!sipIsErr)
        {
            Py_BEGIN_ALLOW_THREADS
            sipRes = new QDBusMessage(sipCpp->callWithArgumentList(QDBus::AutoDetect, *a0, args));
            Py_END_ALLOW_THREADS
        }
%End

    QDBusMessage call(QDBus::CallMode mode, const QString &method, ...);
%MethodCode
        QList<QVariant> args;
        
        args = QtDBus_marshal(a2, &sipIsErr);
        
        if (!sipIsErr)
        {
            Py_BEGIN_ALLOW_THREADS
            sipRes = new QDBusMessage(sipCpp->callWithArgumentList(a0, *a1, args));
            Py_END_ALLOW_THREADS
        }
%End

    QDBusMessage callWithArgumentList(QDBus::CallMode mode, const QString &method, const QList<QVariant> &args) /ReleaseGIL/;
    bool callWithCallback(const QString &method, const QList<QVariant> &args, SIP_PYOBJECT returnMethod /TypeHint="PYQT_SLOT"/, SIP_PYOBJECT errorMethod /TypeHint="PYQT_SLOT"/);
%MethodCode
        QObject *receiver;
        QByteArray return_slot;
        
        if ((sipError = pyqt6_qtdbus_get_pyqtslot_parts(a2, &receiver, return_slot)) == sipErrorNone)
        {
            QObject *error_receiver;
            QByteArray error_slot;
        
            if ((sipError = pyqt6_qtdbus_get_pyqtslot_parts(a3, &error_receiver, error_slot)) == sipErrorNone)
            {
                if (receiver == error_receiver)
                {
                    sipRes = sipCpp->callWithCallback(*a0, *a1, receiver, return_slot.constData(), error_slot.constData());
                }
                else
                {
                    PyErr_SetString(PyExc_ValueError,
                            "the return and error methods must be bound to the same QObject instance");
                    sipError = sipErrorFail;
                }
            }
            else if (sipError == sipErrorContinue)
            {
                sipError = sipBadCallableArg(3, a3);
            }
        }
        else if (sipError == sipErrorContinue)
        {
            sipError = sipBadCallableArg(2, a2);
        }
%End

    bool callWithCallback(const QString &method, QList<QVariant> &args, SIP_PYOBJECT slot /TypeHint="PYQT_SLOT"/);
%MethodCode
        QObject *receiver;
        QByteArray slot;
        
        if ((sipError = pyqt6_qtdbus_get_pyqtslot_parts(a2, &receiver, slot)) == sipErrorNone)
        {
            sipRes = sipCpp->callWithCallback(*a0, *a1, receiver, slot.constData());
        }
        else if (sipError == sipErrorContinue)
        {
            sipError = sipBadCallableArg(2, a2);
        }
%End

    QDBusPendingCall asyncCall(const QString &method, ...);
%MethodCode
        QList<QVariant> args;
        
        args = QtDBus_marshal(a1, &sipIsErr);
        
        if (!sipIsErr)
        {
            Py_BEGIN_ALLOW_THREADS
            sipRes = new QDBusPendingCall(sipCpp->asyncCallWithArgumentList(*a0, args));
            Py_END_ALLOW_THREADS
        }
%End

    QDBusPendingCall asyncCallWithArgumentList(const QString &method, const QList<QVariant> &args);

protected:
    QDBusAbstractInterface(const QString &service, const QString &path, const char *interface, const QDBusConnection &connection, QObject *parent /TransferThis/);
    virtual void connectNotify(const QMetaMethod &signal);
    virtual void disconnectNotify(const QMetaMethod &signal);

public:
%If (Qt_6_7_0 -)
    void setInteractiveAuthorizationAllowed(bool enable);
%End
%If (Qt_6_7_0 -)
    bool isInteractiveAuthorizationAllowed() const;
%End
};

%ModuleHeaderCode
#include "qpydbus_api.h"

// Imports from QtCore.
typedef PyObject *(*pyqt6_qtdbus_from_qvariant_by_type_t)(QVariant &, PyObject *);
extern pyqt6_qtdbus_from_qvariant_by_type_t pyqt6_qtdbus_from_qvariant_by_type;

typedef sipErrorState (*pyqt6_qtdbus_get_pyqtslot_parts_t)(PyObject *, QObject **, QByteArray &);
extern pyqt6_qtdbus_get_pyqtslot_parts_t pyqt6_qtdbus_get_pyqtslot_parts;
%End

%ModuleCode
// Imports from QtCore.
pyqt6_qtdbus_from_qvariant_by_type_t pyqt6_qtdbus_from_qvariant_by_type;
pyqt6_qtdbus_get_pyqtslot_parts_t pyqt6_qtdbus_get_pyqtslot_parts;
%End

%PostInitialisationCode
qpydbus_post_init();

// Imports from QtCore.
pyqt6_qtdbus_from_qvariant_by_type = (pyqt6_qtdbus_from_qvariant_by_type_t)sipImportSymbol("pyqt6_from_qvariant_by_type");
Q_ASSERT(pyqt6_qtdbus_from_qvariant_by_type);

pyqt6_qtdbus_get_pyqtslot_parts = (pyqt6_qtdbus_get_pyqtslot_parts_t)sipImportSymbol("pyqt6_get_pyqtslot_parts");
Q_ASSERT(pyqt6_qtdbus_get_pyqtslot_parts);
%End
