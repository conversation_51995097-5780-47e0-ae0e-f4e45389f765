// qopengldebug.sip generated by MetaSIP
//
// This file is part of the QtOpenGL Python extension module.
//
// Copyright (c) 2025 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt6.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QOpenGLDebugMessage
{
%TypeHeaderCode
#include <qopengldebug.h>
%End

public:
    enum Source /BaseType=Flag/
    {
        InvalidSource,
        APISource,
        WindowSystemSource,
        ShaderCompilerSource,
        ThirdPartySource,
        ApplicationSource,
        OtherSource,
        AnySource,
    };

    typedef QFlags<QOpenGLDebugMessage::Source> Sources;

    enum Type /BaseType=Flag/
    {
        InvalidType,
        ErrorType,
        DeprecatedBehaviorType,
        UndefinedBehaviorType,
        PortabilityType,
        PerformanceType,
        OtherType,
        MarkerType,
        GroupPushType,
        GroupPopType,
        AnyType,
    };

    typedef QFlags<QOpenGLDebugMessage::Type> Types;

    enum Severity /BaseType=Flag/
    {
        InvalidSeverity,
        HighSeverity,
        MediumSeverity,
        LowSeverity,
        NotificationSeverity,
        AnySeverity,
    };

    typedef QFlags<QOpenGLDebugMessage::Severity> Severities;
    QOpenGLDebugMessage();
    QOpenGLDebugMessage(const QOpenGLDebugMessage &debugMessage);
    ~QOpenGLDebugMessage();
    void swap(QOpenGLDebugMessage &debugMessage /Constrained/);
    QOpenGLDebugMessage::Source source() const;
    QOpenGLDebugMessage::Type type() const;
    QOpenGLDebugMessage::Severity severity() const;
    GLuint id() const;
    QString message() const;
    static QOpenGLDebugMessage createApplicationMessage(const QString &text, GLuint id = 0, QOpenGLDebugMessage::Severity severity = QOpenGLDebugMessage::NotificationSeverity, QOpenGLDebugMessage::Type type = QOpenGLDebugMessage::OtherType);
    static QOpenGLDebugMessage createThirdPartyMessage(const QString &text, GLuint id = 0, QOpenGLDebugMessage::Severity severity = QOpenGLDebugMessage::NotificationSeverity, QOpenGLDebugMessage::Type type = QOpenGLDebugMessage::OtherType);
    bool operator==(const QOpenGLDebugMessage &debugMessage) const;
    bool operator!=(const QOpenGLDebugMessage &debugMessage) const;
};

class QOpenGLDebugLogger : public QObject
{
%TypeHeaderCode
#include <qopengldebug.h>
%End

%ConvertToSubClassCode
    static struct class_graph {
        const char *name;
        sipTypeDef **type;
        int yes, no;
    } graph[] = {
        {sipName_QOpenGLDebugLogger, &sipType_QOpenGLDebugLogger, -1, 1},
        {sipName_QOpenGLShader, &sipType_QOpenGLShader, -1, 2},
        {sipName_QOpenGLShaderProgram, &sipType_QOpenGLShaderProgram, -1, 3},
    #if !defined(SIP_FEATURE_PyQt_OpenGL_ES2)
        {sipName_QOpenGLTimeMonitor, &sipType_QOpenGLTimeMonitor, -1, 4},
    #else
        {0, 0, -1, 4},
    #endif
    #if !defined(SIP_FEATURE_PyQt_OpenGL_ES2)
        {sipName_QOpenGLTimerQuery, &sipType_QOpenGLTimerQuery, -1, 5},
    #else
        {0, 0, -1, 5},
    #endif
        {sipName_QOpenGLVertexArrayObject, &sipType_QOpenGLVertexArrayObject, -1, 6},
        {sipName_QOpenGLWindow, &sipType_QOpenGLWindow, -1, -1},
    };
    
    int i = 0;
    
    sipType = NULL;
    
    do
    {
        struct class_graph *cg = &graph[i];
    
        if (cg->name != NULL && sipCpp->inherits(cg->name))
        {
            sipType = *cg->type;
            i = cg->yes;
        }
        else
            i = cg->no;
    }
    while (i >= 0);
%End

public:
    enum LoggingMode
    {
        AsynchronousLogging,
        SynchronousLogging,
    };

    explicit QOpenGLDebugLogger(QObject *parent /TransferThis/ = 0);
    virtual ~QOpenGLDebugLogger();
    bool initialize();
    bool isLogging() const;
    QOpenGLDebugLogger::LoggingMode loggingMode() const;
    qint64 maximumMessageLength() const;
    void pushGroup(const QString &name, GLuint id = 0, QOpenGLDebugMessage::Source source = QOpenGLDebugMessage::ApplicationSource);
    void popGroup();
    void enableMessages(QOpenGLDebugMessage::Sources sources = QOpenGLDebugMessage::AnySource, QOpenGLDebugMessage::Types types = QOpenGLDebugMessage::AnyType, QOpenGLDebugMessage::Severities severities = QOpenGLDebugMessage::AnySeverity);
    void enableMessages(const QList<unsigned int> &ids, QOpenGLDebugMessage::Sources sources = QOpenGLDebugMessage::AnySource, QOpenGLDebugMessage::Types types = QOpenGLDebugMessage::AnyType);
    void disableMessages(QOpenGLDebugMessage::Sources sources = QOpenGLDebugMessage::AnySource, QOpenGLDebugMessage::Types types = QOpenGLDebugMessage::AnyType, QOpenGLDebugMessage::Severities severities = QOpenGLDebugMessage::AnySeverity);
    void disableMessages(const QList<unsigned int> &ids, QOpenGLDebugMessage::Sources sources = QOpenGLDebugMessage::AnySource, QOpenGLDebugMessage::Types types = QOpenGLDebugMessage::AnyType);
    QList<QOpenGLDebugMessage> loggedMessages() const;

public slots:
    void logMessage(const QOpenGLDebugMessage &debugMessage);
    void startLogging(QOpenGLDebugLogger::LoggingMode loggingMode = QOpenGLDebugLogger::AsynchronousLogging);
    void stopLogging();

signals:
    void messageLogged(const QOpenGLDebugMessage &debugMessage);
};
