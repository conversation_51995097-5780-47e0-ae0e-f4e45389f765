// QtPositioningmod.sip generated by MetaSIP
//
// This file is part of the QtPositioning Python extension module.
//
// Copyright (c) 2025 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt6.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


%Module(name=PyQt6.QtPositioning, keyword_arguments="Optional", use_limited_api=True)

%Import QtCore/QtCoremod.sip

%Copying
Copyright (c) 2025 Riverbank Computing Limited <<EMAIL>>

This file is part of PyQt6.

This file may be used under the terms of the GNU General Public License
version 3.0 as published by the Free Software Foundation and appearing in
the file LICENSE included in the packaging of this file.  Please review the
following information to ensure the GNU General Public License version 3.0
requirements will be met: http://www.gnu.org/copyleft/gpl.html.

If you do not wish to use this file under the terms of the GPL version 3.0
then you may purchase a commercial license.  For more information contact
<EMAIL>.

This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.
%End

%Include qgeoaddress.sip
%Include qgeoareamonitorinfo.sip
%Include qgeoareamonitorsource.sip
%Include qgeocircle.sip
%Include qgeocoordinate.sip
%Include qgeolocation.sip
%Include qgeopath.sip
%Include qgeopolygon.sip
%Include qgeopositioninfo.sip
%Include qgeopositioninfosource.sip
%Include qgeorectangle.sip
%Include qgeosatelliteinfo.sip
%Include qgeosatelliteinfosource.sip
%Include qgeoshape.sip
%Include qnmeapositioninfosource.sip
%Include qnmeasatelliteinfosource.sip
