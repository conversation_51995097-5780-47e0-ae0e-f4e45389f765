// qnmeasatelliteinfosource.sip generated by MetaSIP
//
// This file is part of the QtPositioning Python extension module.
//
// Copyright (c) 2025 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt6.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


%If (Qt_6_2_0 -)

class QNmeaSatelliteInfoSource : public QGeoSatelliteInfoSource
{
%TypeHeaderCode
#include <qnmeasatelliteinfosource.h>
%End

public:
    enum class UpdateMode
    {
        RealTimeMode,
        SimulationMode,
    };

    static QString SimulationUpdateInterval;
    QNmeaSatelliteInfoSource(QNmeaSatelliteInfoSource::UpdateMode mode, QObject *parent /TransferThis/ = 0);
    virtual ~QNmeaSatelliteInfoSource();
    QNmeaSatelliteInfoSource::UpdateMode updateMode() const;
    void setDevice(QIODevice *source);
    QIODevice *device() const;
    virtual void setUpdateInterval(int msec);
    virtual int minimumUpdateInterval() const;
    virtual QGeoSatelliteInfoSource::Error error() const;
    virtual bool setBackendProperty(const QString &name, const QVariant &value);
    virtual QVariant backendProperty(const QString &name) const;

public slots:
    virtual void startUpdates();
    virtual void stopUpdates();
    virtual void requestUpdate(int timeout = 0);

protected:
    virtual QGeoSatelliteInfo::SatelliteSystem parseSatellitesInUseFromNmea(const char *data /Encoding="None"/, int size, QList<int> &pnrsInUse);

    enum SatelliteInfoParseStatus
    {
        NotParsed,
        PartiallyParsed,
        FullyParsed,
    };

    virtual QNmeaSatelliteInfoSource::SatelliteInfoParseStatus parseSatelliteInfoFromNmea(const char *data /Encoding="None"/, int size, QList<QGeoSatelliteInfo> &infos, QGeoSatelliteInfo::SatelliteSystem &system);
};

%End
