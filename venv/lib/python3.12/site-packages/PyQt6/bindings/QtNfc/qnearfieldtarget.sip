// qnearfieldtarget.sip generated by MetaSIP
//
// This file is part of the QtNfc Python extension module.
//
// Copyright (c) 2025 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt6.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICEN<PERSON> included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


%If (Qt_6_2_0 -)

class QNearFieldTarget : public QObject
{
%TypeHeaderCode
#include <qnearfieldtarget.h>
%End

public:
    enum Type
    {
        ProprietaryTag,
        NfcTagType1,
        NfcTagType2,
        NfcTagType3,
        NfcTagType4,
        NfcTagType4A,
        NfcTagType4B,
        MifareTag,
    };

    enum AccessMethod /BaseType=Flag/
    {
        UnknownAccess,
        NdefAccess,
        TagTypeSpecificAccess,
        AnyAccess,
    };

    typedef QFlags<QNearFieldTarget::AccessMethod> AccessMethods;

    enum Error
    {
        NoError,
        UnknownError,
        UnsupportedError,
        TargetOutOfRangeError,
        NoResponseError,
        ChecksumMismatchError,
        InvalidParametersError,
        NdefReadError,
        NdefWriteError,
        CommandError,
        ConnectionError,
        TimeoutError,
%If (Qt_6_8_0 -)
        UnsupportedTargetError,
%End
    };

    class RequestId
    {
%TypeHeaderCode
#include <qnearfieldtarget.h>
%End

    public:
        RequestId();
        RequestId(const QNearFieldTarget::RequestId &other);
        ~RequestId();
        bool isValid() const;
        int refCount() const;
        bool operator<(const QNearFieldTarget::RequestId &other) const;
        bool operator==(const QNearFieldTarget::RequestId &other) const;
        bool operator!=(const QNearFieldTarget::RequestId &other) const;
    };

    explicit QNearFieldTarget(QObject *parent /TransferThis/ = 0);
    virtual ~QNearFieldTarget();
    QByteArray uid() const;
    QNearFieldTarget::Type type() const;
    QNearFieldTarget::AccessMethods accessMethods() const;
    bool hasNdefMessage();
    QNearFieldTarget::RequestId readNdefMessages();
    QNearFieldTarget::RequestId writeNdefMessages(const QList<QNdefMessage> &messages);
    QNearFieldTarget::RequestId sendCommand(const QByteArray &command);
    bool waitForRequestCompleted(const QNearFieldTarget::RequestId &id, int msecs = 5000) /ReleaseGIL/;
    QVariant requestResponse(const QNearFieldTarget::RequestId &id) const;

signals:
    void disconnected();
    void ndefMessageRead(const QNdefMessage &message);
    void requestCompleted(const QNearFieldTarget::RequestId &id);
    void error(QNearFieldTarget::Error error, const QNearFieldTarget::RequestId &id);

public:
    bool disconnect();
    int maxCommandLength() const;
};

%End
