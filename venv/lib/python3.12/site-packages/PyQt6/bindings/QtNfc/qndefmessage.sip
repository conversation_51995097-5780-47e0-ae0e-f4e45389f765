// qndefmessage.sip generated by MetaSIP
//
// This file is part of the QtNfc Python extension module.
//
// Copyright (c) 2025 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt6.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


%If (Qt_6_2_0 -)

class QNdefMessage
{
%TypeHeaderCode
#include <qndefmessage.h>
%End

public:
    QNdefMessage();
    explicit QNdefMessage(const QNdefRecord &record);
    QNdefMessage(const QNdefMessage &message);
    QNdefMessage(const QList<QNdefRecord> &records);
    bool operator==(const QNdefMessage &other) const;
    QByteArray toByteArray() const;
    Py_ssize_t __len__() const;
%MethodCode
        sipRes = sipCpp->count();
%End

    QNdefRecord __getitem__(int i) const;
%MethodCode
        Py_ssize_t idx = sipConvertFromSequenceIndex(a0, sipCpp->count());
        
        if (idx < 0)
            sipIsErr = 1;
        else
            sipRes = new QNdefRecord(sipCpp->at((int)idx));
%End

    void __setitem__(int i, const QNdefRecord &value);
%MethodCode
        int len = sipCpp->count();
        
        if ((a0 = (int)sipConvertFromSequenceIndex(a0, len)) < 0)
            sipIsErr = 1;
        else
            (*sipCpp)[a0] = *a1;
%End

    void __delitem__(int i);
%MethodCode
        if ((a0 = (int)sipConvertFromSequenceIndex(a0, sipCpp->count())) < 0)
            sipIsErr = 1;
        else
            sipCpp->removeAt(a0);
%End

    static QNdefMessage fromByteArray(const QByteArray &message);
};

%End
