// qsgrendernode.sip generated by MetaSIP
//
// This file is part of the QtQuick Python extension module.
//
// Copyright (c) 2025 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt6.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICEN<PERSON> included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QSGRenderNode : public QSGNode
{
%TypeHeaderCode
#include <qsgrendernode.h>
%End

public:
    enum StateFlag /BaseType=Flag/
    {
        DepthState,
        StencilState,
        ScissorState,
        ColorState,
        BlendState,
        CullState,
        ViewportState,
        RenderTargetState,
    };

    typedef QFlags<QSGRenderNode::StateFlag> StateFlags;

    enum RenderingFlag /BaseType=Flag/
    {
        BoundedRectRendering,
        DepthAwareRendering,
        OpaqueRendering,
    };

    typedef QFlags<QSGRenderNode::RenderingFlag> RenderingFlags;

    struct RenderState /NoDefaultCtors/
    {
%TypeHeaderCode
#include <qsgrendernode.h>
%End

        virtual ~RenderState();
        virtual const QMatrix4x4 *projectionMatrix() const = 0;
        virtual QRect scissorRect() const = 0;
        virtual bool scissorEnabled() const = 0;
        virtual int stencilValue() const = 0;
        virtual bool stencilEnabled() const = 0;
        virtual const QRegion *clipRegion() const = 0;
        virtual void *get(const char *state) const;
    };

    virtual ~QSGRenderNode();
    virtual QSGRenderNode::StateFlags changedStates() const;
    virtual void render(const QSGRenderNode::RenderState *state) = 0;
    virtual void releaseResources();
    virtual QSGRenderNode::RenderingFlags flags() const;
    virtual QRectF rect() const;
    const QMatrix4x4 *matrix() const;
    const QSGClipNode *clipList() const;
    qreal inheritedOpacity() const;
    virtual void prepare();
%If (Qt_6_5_0 -)
    const QMatrix4x4 *projectionMatrix() const;
%End
};
