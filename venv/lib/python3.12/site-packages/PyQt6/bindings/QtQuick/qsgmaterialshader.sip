// qsgmaterialshader.sip generated by MetaSIP
//
// This file is part of the QtQuick Python extension module.
//
// Copyright (c) 2025 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt6.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QSGMaterialShader
{
%TypeHeaderCode
#include <qsgmaterialshader.h>
%End

public:
    class RenderState
    {
%TypeHeaderCode
#include <qsgmaterialshader.h>
%End

    public:
        enum DirtyState /BaseType=Flag/
        {
            DirtyMatrix,
            DirtyOpacity,
            DirtyCachedMaterialData,
            DirtyAll,
        };

        typedef QFlags<QSGMaterialShader::RenderState::DirtyState> DirtyStates;
        QSGMaterialShader::RenderState::DirtyStates dirtyStates() const;
        bool isMatrixDirty() const;
        bool isOpacityDirty() const;
        float opacity() const;
        QMatrix4x4 combinedMatrix() const;
        QMatrix4x4 modelViewMatrix() const;
        QMatrix4x4 projectionMatrix() const;
        QRect viewportRect() const;
        QRect deviceRect() const;
        float determinant() const;
        float devicePixelRatio() const;
        QByteArray *uniformData();
    };

    struct GraphicsPipelineState
    {
%TypeHeaderCode
#include <qsgmaterialshader.h>
%End

        enum BlendFactor
        {
            Zero,
            One,
            SrcColor,
            OneMinusSrcColor,
            DstColor,
            OneMinusDstColor,
            SrcAlpha,
            OneMinusSrcAlpha,
            DstAlpha,
            OneMinusDstAlpha,
            ConstantColor,
            OneMinusConstantColor,
            ConstantAlpha,
            OneMinusConstantAlpha,
            SrcAlphaSaturate,
            Src1Color,
            OneMinusSrc1Color,
            Src1Alpha,
            OneMinusSrc1Alpha,
        };

%If (Qt_6_8_0 -)

        enum class BlendOp
        {
            Add,
            Subtract,
            ReverseSubtract,
            Min,
            Max,
        };

%End

        enum ColorMaskComponent /BaseType=Flag/
        {
            R,
            G,
            B,
            A,
        };

        typedef QFlags<QSGMaterialShader::GraphicsPipelineState::ColorMaskComponent> ColorMask;

        enum CullMode
        {
            CullNone,
            CullFront,
            CullBack,
        };

%If (Qt_6_4_0 -)

        enum PolygonMode
        {
            Fill,
            Line,
        };

%End
        QColor blendConstant;
        bool blendEnable;
        QSGMaterialShader::GraphicsPipelineState::ColorMask colorWrite;
        QSGMaterialShader::GraphicsPipelineState::CullMode cullMode;
%If (Qt_6_5_0 -)
        QSGMaterialShader::GraphicsPipelineState::BlendFactor dstAlpha;
%End
        QSGMaterialShader::GraphicsPipelineState::BlendFactor dstColor;
%If (Qt_6_4_0 -)
        QSGMaterialShader::GraphicsPipelineState::PolygonMode polygonMode;
%End
%If (Qt_6_5_0 -)
        QSGMaterialShader::GraphicsPipelineState::BlendFactor srcAlpha;
%End
        QSGMaterialShader::GraphicsPipelineState::BlendFactor srcColor;
%If (Qt_6_8_0 -)
        QSGMaterialShader::GraphicsPipelineState::BlendOp opAlpha;
%End
%If (Qt_6_8_0 -)
        QSGMaterialShader::GraphicsPipelineState::BlendOp opColor;
%End
%If (Qt_6_5_0 -)
        bool separateBlendFactors;
%End
    };

    enum Flag /BaseType=Flag/
    {
        UpdatesGraphicsPipelineState,
    };

    typedef QFlags<QSGMaterialShader::Flag> Flags;

    enum Stage
    {
        VertexStage,
        FragmentStage,
    };

    QSGMaterialShader();
    virtual ~QSGMaterialShader();
    virtual bool updateUniformData(QSGMaterialShader::RenderState &state, QSGMaterial *newMaterial, QSGMaterial *oldMaterial);
    virtual void updateSampledImage(QSGMaterialShader::RenderState &state, int binding, QSGTexture **texture /Out/, QSGMaterial *newMaterial, QSGMaterial *oldMaterial);
    virtual bool updateGraphicsPipelineState(QSGMaterialShader::RenderState &state, QSGMaterialShader::GraphicsPipelineState *ps, QSGMaterial *newMaterial, QSGMaterial *oldMaterial);
    QSGMaterialShader::Flags flags() const;
    void setFlag(QSGMaterialShader::Flags flags, bool on = true);
    void setFlags(QSGMaterialShader::Flags flags);
%If (Qt_6_4_0 -)
    int combinedImageSamplerCount(int binding) const;
%End

protected:
    void setShaderFileName(QSGMaterialShader::Stage stage, const QString &filename);
%If (Qt_6_8_0 -)
    void setShaderFileName(QSGMaterialShader::Stage stage, const QString &filename, int viewCount);
%End

private:
    QSGMaterialShader(const QSGMaterialShader &);
};
