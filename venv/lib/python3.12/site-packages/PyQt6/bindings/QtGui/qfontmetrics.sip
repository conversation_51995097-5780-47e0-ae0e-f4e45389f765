// qfontmetrics.sip generated by MetaSIP
//
// This file is part of the QtGui Python extension module.
//
// Copyright (c) 2025 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt6.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QFontMetrics
{
%TypeHeaderCode
#include <qfontmetrics.h>
%End

public:
    explicit QFontMetrics(const QFont &);
    QFontMetrics(const QFont &font, const QPaintDevice *pd);
    QFontMetrics(const QFontMetrics &);
    ~QFontMetrics();
    int ascent() const;
    int descent() const;
    int height() const;
    int leading() const;
    int lineSpacing() const;
    int minLeftBearing() const;
    int minRightBearing() const;
    int maxWidth() const;
    int xHeight() const;
    bool inFont(QChar) const;
    int leftBearing(QChar) const;
    int rightBearing(QChar) const;
%If (Qt_6_3_0 -)
    QRect boundingRect(const QString &text, const QTextOption &textOption) const;
%End
    QRect boundingRect(QChar) const /PyName=boundingRectChar/;
    QRect boundingRect(const QString &text) const;
    QRect boundingRect(const QRect &rect, int flags, const QString &text, int tabStops = 0, SIP_PYLIST tabArray /AllowNone,TypeHint="Optional[List[int]]"/ = 0) const;
%MethodCode
        int *tabarray = qtgui_tabarray(a4);
        
        sipRes = new QRect(sipCpp->boundingRect(*a0, a1, *a2, a3, tabarray));
        
        if (!tabarray)
            delete[] tabarray;
%End

    QRect boundingRect(int x, int y, int width, int height, int flags, const QString &text, int tabStops = 0, SIP_PYLIST tabArray /AllowNone,TypeHint="Optional[List[int]]"/ = 0) const;
%MethodCode
        int *tabarray = qtgui_tabarray(a7);
        
        sipRes = new QRect(sipCpp->boundingRect(a0, a1, a2, a3, a4, *a5, a6, tabarray));
        
        if (!tabarray)
            delete[] tabarray;
%End

    QSize size(int flags, const QString &text, int tabStops = 0, SIP_PYLIST tabArray /AllowNone,TypeHint="Optional[List[int]]"/ = 0) const;
%MethodCode
        int *tabarray = qtgui_tabarray(a3);
        
        sipRes = new QSize(sipCpp->size(a0, *a1, a2, tabarray));
        
        if (!tabarray)
            delete[] tabarray;
%End

    int underlinePos() const;
    int overlinePos() const;
    int strikeOutPos() const;
    int lineWidth() const;
    int averageCharWidth() const;
    QString elidedText(const QString &text, Qt::TextElideMode mode, int width, int flags = 0) const;
    bool operator==(const QFontMetrics &other) const;
    bool operator!=(const QFontMetrics &other) const;
%If (Qt_6_3_0 -)
    QRect tightBoundingRect(const QString &text, const QTextOption &textOption) const;
%End
    QRect tightBoundingRect(const QString &text) const;
    bool inFontUcs4(uint character) const;
    void swap(QFontMetrics &other /Constrained/);
    int capHeight() const;
%If (Qt_6_3_0 -)
    int horizontalAdvance(const QString &, const QTextOption &textOption) const;
%End
    int horizontalAdvance(const QString &, int length = -1) const;
    qreal fontDpi() const;
};

class QFontMetricsF
{
%TypeHeaderCode
#include <qfontmetrics.h>
%End

public:
    explicit QFontMetricsF(const QFont &);
    QFontMetricsF(const QFont &font, const QPaintDevice *pd);
    QFontMetricsF(const QFontMetrics &);
    QFontMetricsF(const QFontMetricsF &);
    ~QFontMetricsF();
    qreal ascent() const;
    qreal descent() const;
    qreal height() const;
    qreal leading() const;
    qreal lineSpacing() const;
    qreal minLeftBearing() const;
    qreal minRightBearing() const;
    qreal maxWidth() const;
    qreal xHeight() const;
    bool inFont(QChar) const;
    qreal leftBearing(QChar) const;
    qreal rightBearing(QChar) const;
%If (Qt_6_3_0 -)
    QRectF boundingRect(const QString &text, const QTextOption &textOption) const;
%End
    QRectF boundingRect(QChar) const /PyName=boundingRectChar/;
    QRectF boundingRect(const QString &string) const;
    QRectF boundingRect(const QRectF &rect, int flags, const QString &text, int tabStops = 0, SIP_PYLIST tabArray /AllowNone,TypeHint="Optional[List[int]]"/ = 0) const;
%MethodCode
        int *tabarray = qtgui_tabarray(a4);
        
        sipRes = new QRectF(sipCpp->boundingRect(*a0, a1, *a2, a3, tabarray));
        
        if (!tabarray)
            delete[] tabarray;
%End

    QSizeF size(int flags, const QString &text, int tabStops = 0, SIP_PYLIST tabArray /AllowNone,TypeHint="Optional[List[int]]"/ = 0) const;
%MethodCode
        int *tabarray = qtgui_tabarray(a3);
        
        sipRes = new QSizeF(sipCpp->size(a0, *a1, a2, tabarray));
        
        if (!tabarray)
            delete[] tabarray;
%End

    qreal underlinePos() const;
    qreal overlinePos() const;
    qreal strikeOutPos() const;
    qreal lineWidth() const;
    qreal averageCharWidth() const;
    QString elidedText(const QString &text, Qt::TextElideMode mode, qreal width, int flags = 0) const;
    bool operator==(const QFontMetricsF &other) const;
    bool operator!=(const QFontMetricsF &other) const;
%If (Qt_6_3_0 -)
    QRectF tightBoundingRect(const QString &text, const QTextOption &textOption) const;
%End
    QRectF tightBoundingRect(const QString &text) const;
    bool inFontUcs4(uint character) const;
    void swap(QFontMetricsF &other /Constrained/);
    qreal capHeight() const;
%If (Qt_6_3_0 -)
    qreal horizontalAdvance(const QString &string, const QTextOption &textOption) const;
%End
    qreal horizontalAdvance(const QString &string, int length = -1) const;
    qreal fontDpi() const;
};

%ModuleHeaderCode
// Used by QFontMetrics and QFontMetricsF.
int *qtgui_tabarray(PyObject *l);
%End

%ModuleCode
// Convert an optional Python list to a 0 terminated array of integers on the
// heap.
int *qtgui_tabarray(PyObject *l)
{
    if (!l || l == Py_None)
        return 0;

    int *arr = new int[PyList_Size(l) + 1];
    Py_ssize_t i;

    for (i = 0; i < PyList_Size(l); ++i)
        arr[i] = PyLong_AsLong(PyList_GetItem(l, i));

    arr[i] = 0;

    return arr;
}
%End
