// qstylehints.sip generated by MetaSIP
//
// This file is part of the QtGui Python extension module.
//
// Copyright (c) 2025 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt6.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QStyleHints : public QObject
{
%TypeHeaderCode
#include <qstylehints.h>
%End

public:
    int mouseDoubleClickInterval() const;
    int startDragDistance() const;
    int startDragTime() const;
    int startDragVelocity() const;
    int keyboardInputInterval() const;
    int keyboardAutoRepeatRate() const;
    int cursorFlashTime() const;
    bool showIsFullScreen() const;
    int passwordMaskDelay() const;
    qreal fontSmoothingGamma() const;
    bool useRtlExtensions() const;
    QChar passwordMaskCharacter() const;
    bool setFocusOnTouchRelease() const;
    int mousePressAndHoldInterval() const;
    Qt::TabFocusBehavior tabFocusBehavior() const;
    bool singleClickActivation() const;

signals:
    void cursorFlashTimeChanged(int cursorFlashTime);
    void keyboardInputIntervalChanged(int keyboardInputInterval);
    void mouseDoubleClickIntervalChanged(int mouseDoubleClickInterval);
    void startDragDistanceChanged(int startDragDistance);
    void startDragTimeChanged(int startDragTime);
    void mousePressAndHoldIntervalChanged(int mousePressAndHoldInterval);
    void tabFocusBehaviorChanged(Qt::TabFocusBehavior tabFocusBehavior);

public:
    bool showIsMaximized() const;
    bool useHoverEffects() const;
    void setUseHoverEffects(bool useHoverEffects);

signals:
    void useHoverEffectsChanged(bool useHoverEffects);

public:
    int wheelScrollLines() const;

signals:
    void wheelScrollLinesChanged(int scrollLines);

public:
    bool showShortcutsInContextMenus() const;
    int mouseQuickSelectionThreshold() const;

signals:
    void mouseQuickSelectionThresholdChanged(int threshold);

public:
    void setShowShortcutsInContextMenus(bool showShortcutsInContextMenus);

signals:
    void showShortcutsInContextMenusChanged(bool);

public:
    int mouseDoubleClickDistance() const;
    int touchDoubleTapDistance() const;
%If (Qt_6_5_0 -)
    qreal keyboardAutoRepeatRateF() const;
%End
%If (Qt_6_5_0 -)
    Qt::ColorScheme colorScheme() const;
%End

signals:
%If (Qt_6_5_0 -)
    void colorSchemeChanged(Qt::ColorScheme colorScheme);
%End

public:
%If (Qt_6_8_0 -)
    Qt::ContextMenuTrigger contextMenuTrigger() const;
%End
%If (Qt_6_8_0 -)
    void setContextMenuTrigger(Qt::ContextMenuTrigger contextMenuTrigger);
%End
%If (Qt_6_8_0 -)
    void setColorScheme(Qt::ColorScheme scheme);
%End
%If (Qt_6_8_0 -)
    void unsetColorScheme();
%End

signals:
%If (Qt_6_8_0 -)
    void contextMenuTriggerChanged(Qt::ContextMenuTrigger contextMenuTrigger);
%End

private:
    QStyleHints();
};
