// qcolor.sip generated by MetaSIP
//
// This file is part of the QtGui Python extension module.
//
// Copyright (c) 2025 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt6.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QColor /TypeHintIn="Union[QColor, Qt.GlobalColor, int]"/
{
%TypeHeaderCode
#include <qcolor.h>
%End

%ConvertToTypeCode
// SIP doesn't support automatic type convertors so we explicitly allow a
// Qt::GlobalColor or an (unsigned) int to be used whenever a QColor is
// expected.  Note that SIP must process QColor before QBrush so that the
// former's QVariant cast operator is applied before the latter's.

if (PyLong_Check(sipPy))
{
    if (sipIsErr == NULL)
        return 1;

    unsigned long argb = PyLong_AsUnsignedLong(sipPy);

    if (PyErr_Occurred())
    {
        PyErr_Clear();
        *sipIsErr = 1;
        return 0;
    }

    *sipCppPtr = new QColor(static_cast<QRgb>(argb));

    return sipGetState(sipTransferObj);
}

bool is_global_color = true;
int global_color = sipConvertToEnum(sipPy, sipType_Qt_GlobalColor);

if (PyErr_Occurred())
{
    PyErr_Clear();
    is_global_color = false;
}

if (sipIsErr == NULL)
    return (is_global_color ||
            sipCanConvertToType(sipPy, sipType_QColor, SIP_NO_CONVERTORS));

if (is_global_color)
{
    *sipCppPtr = new QColor(static_cast<Qt::GlobalColor>(global_color));

    return sipGetState(sipTransferObj);
}

*sipCppPtr = reinterpret_cast<QColor *>(sipConvertToType(sipPy, sipType_QColor, sipTransferObj, SIP_NO_CONVERTORS, 0, sipIsErr));

return 0;
%End

%PickleCode
    sipRes = Py_BuildValue("iiii", sipCpp->red(), sipCpp->green(), sipCpp->blue(), sipCpp->alpha());
%End

public:
    enum Spec
    {
        Invalid,
        Rgb,
        Hsv,
        Cmyk,
        Hsl,
        ExtendedRgb,
    };

    QColor(Qt::GlobalColor color);
    QColor(QRgb rgb);
    QColor(QRgba64 rgba64);
    QColor(const QVariant &variant /GetWrapper/) /NoDerived/;
%MethodCode
        if (a0->canConvert<QColor>())
            sipCpp = new QColor(a0->value<QColor>());
        else
            sipError = sipBadCallableArg(0, a0Wrapper);
%End

    QString name(QColor::NameFormat format = QColor::HexRgb) const;
    void setNamedColor(QStringView name);
    static QStringList colorNames();
    QColor::Spec spec() const;
    int alpha() const;
    void setAlpha(int alpha);
    float alphaF() const;
    void setAlphaF(float alpha);
    int red() const;
    int green() const;
    int blue() const;
    void setRed(int red);
    void setGreen(int green);
    void setBlue(int blue);
    float redF() const;
    float greenF() const;
    float blueF() const;
    void setRedF(float red);
    void setGreenF(float green);
    void setBlueF(float blue);
    void getRgb(int *r, int *g, int *b, int *alpha = 0) const;
    void setRgb(int r, int g, int b, int alpha = 255);
    void getRgbF(float *r, float *g, float *b, float *alpha = 0) const;
    void setRgbF(float r, float g, float b, float alpha = 1.);
    QRgb rgba() const;
    void setRgba(QRgb rgba);
    QRgb rgb() const;
    void setRgb(QRgb rgb);
    int hue() const;
    int saturation() const;
    int value() const;
    float hueF() const;
    float saturationF() const;
    float valueF() const;
    void getHsv(int *h, int *s, int *v, int *alpha = 0) const;
    void setHsv(int h, int s, int v, int alpha = 255);
    void getHsvF(float *h, float *s, float *v, float *alpha = 0) const;
    void setHsvF(float h, float s, float v, float alpha = 1.);
    int cyan() const;
    int magenta() const;
    int yellow() const;
    int black() const;
    float cyanF() const;
    float magentaF() const;
    float yellowF() const;
    float blackF() const;
    void getCmyk(int *c, int *m, int *y, int *k, int *alpha = 0) const;
    void setCmyk(int c, int m, int y, int k, int alpha = 255);
    void getCmykF(float *c, float *m, float *y, float *k, float *alpha = 0) const;
    void setCmykF(float c, float m, float y, float k, float alpha = 1.);
    QColor toRgb() const;
    QColor toHsv() const;
    QColor toCmyk() const;
    QColor convertTo(QColor::Spec colorSpec) const;
    static QColor fromRgb(QRgb rgb);
    static QColor fromRgba(QRgb rgba);
    static QColor fromRgb(int r, int g, int b, int alpha = 255);
    static QColor fromRgbF(float r, float g, float b, float alpha = 1.);
    static QColor fromHsv(int h, int s, int v, int alpha = 255);
    static QColor fromHsvF(float h, float s, float v, float alpha = 1.);
    static QColor fromCmyk(int c, int m, int y, int k, int alpha = 255);
    static QColor fromCmykF(float c, float m, float y, float k, float alpha = 1.);
    bool operator==(const QColor &c) const;
    bool operator!=(const QColor &c) const;
    QColor();
    QColor(int r, int g, int b, int alpha = 255);
    explicit QColor(QStringView name);
    bool isValid() const;
    QColor lighter(int factor = 150) const;
    QColor darker(int factor = 200) const;
    int hsvHue() const;
    int hsvSaturation() const;
    float hsvHueF() const;
    float hsvSaturationF() const;
    int hslHue() const;
    int hslSaturation() const;
    int lightness() const;
    float hslHueF() const;
    float hslSaturationF() const;
    float lightnessF() const;
    void getHsl(int *h, int *s, int *l, int *alpha = 0) const;
    void setHsl(int h, int s, int l, int alpha = 255);
    void getHslF(float *h, float *s, float *l, float *alpha = 0) const;
    void setHslF(float h, float s, float l, float alpha = 1.);
    QColor toHsl() const;
    static QColor fromHsl(int h, int s, int l, int alpha = 255);
    static QColor fromHslF(float h, float s, float l, float alpha = 1.);
    static bool isValidColor(const QString &name);

    enum NameFormat
    {
        HexRgb,
        HexArgb,
    };

    QRgba64 rgba64() const;
    void setRgba64(QRgba64 rgba);
    static QColor fromRgba64(ushort r, ushort g, ushort b, ushort alpha = USHRT_MAX);
    static QColor fromRgba64(QRgba64 rgba);
    QColor toExtendedRgb() const;
%If (Qt_6_4_0 -)
    static QColor fromString(QAnyStringView name);
%End
%If (Qt_6_4_0 -)
    static bool isValidColorName(QAnyStringView);
%End
};

QDataStream &operator<<(QDataStream &, const QColor &) /ReleaseGIL/;
QDataStream &operator>>(QDataStream &, QColor & /Constrained/) /ReleaseGIL/;

namespace QColorConstants
{
%TypeHeaderCode
#include <qcolor.h>
%End

    const QColor Color0;
    const QColor Color1;
    const QColor Black;
    const QColor White;
    const QColor DarkGray;
    const QColor Gray;
    const QColor LightGray;
    const QColor Red;
    const QColor Green;
    const QColor Blue;
    const QColor Cyan;
    const QColor Magenta;
    const QColor Yellow;
    const QColor DarkRed;
    const QColor DarkGreen;
    const QColor DarkBlue;
    const QColor DarkCyan;
    const QColor DarkMagenta;
    const QColor DarkYellow;
    const QColor Transparent;

    namespace Svg
    {
%TypeHeaderCode
#include <qcolor.h>
%End

        const QColor aliceblue;
        const QColor antiquewhite;
        const QColor aqua;
        const QColor aquamarine;
        const QColor azure;
        const QColor beige;
        const QColor bisque;
        const QColor black;
        const QColor blanchedalmond;
        const QColor blue;
        const QColor blueviolet;
        const QColor brown;
        const QColor burlywood;
        const QColor cadetblue;
        const QColor chartreuse;
        const QColor chocolate;
        const QColor coral;
        const QColor cornflowerblue;
        const QColor cornsilk;
        const QColor crimson;
        const QColor cyan;
        const QColor darkblue;
        const QColor darkcyan;
        const QColor darkgoldenrod;
        const QColor darkgray;
        const QColor darkgreen;
        const QColor darkgrey;
        const QColor darkkhaki;
        const QColor darkmagenta;
        const QColor darkolivegreen;
        const QColor darkorange;
        const QColor darkorchid;
        const QColor darkred;
        const QColor darksalmon;
        const QColor darkseagreen;
        const QColor darkslateblue;
        const QColor darkslategray;
        const QColor darkslategrey;
        const QColor darkturquoise;
        const QColor darkviolet;
        const QColor deeppink;
        const QColor deepskyblue;
        const QColor dimgray;
        const QColor dimgrey;
        const QColor dodgerblue;
        const QColor firebrick;
        const QColor floralwhite;
        const QColor forestgreen;
        const QColor fuchsia;
        const QColor gainsboro;
        const QColor ghostwhite;
        const QColor gold;
        const QColor goldenrod;
        const QColor gray;
        const QColor green;
        const QColor greenyellow;
        const QColor grey;
        const QColor honeydew;
        const QColor hotpink;
        const QColor indianred;
        const QColor indigo;
        const QColor ivory;
        const QColor khaki;
        const QColor lavender;
        const QColor lavenderblush;
        const QColor lawngreen;
        const QColor lemonchiffon;
        const QColor lightblue;
        const QColor lightcoral;
        const QColor lightcyan;
        const QColor lightgoldenrodyellow;
        const QColor lightgray;
        const QColor lightgreen;
        const QColor lightgrey;
        const QColor lightpink;
        const QColor lightsalmon;
        const QColor lightseagreen;
        const QColor lightskyblue;
        const QColor lightslategray;
        const QColor lightslategrey;
        const QColor lightsteelblue;
        const QColor lightyellow;
        const QColor lime;
        const QColor limegreen;
        const QColor linen;
        const QColor magenta;
        const QColor maroon;
        const QColor mediumaquamarine;
        const QColor mediumblue;
        const QColor mediumorchid;
        const QColor mediumpurple;
        const QColor mediumseagreen;
        const QColor mediumslateblue;
        const QColor mediumspringgreen;
        const QColor mediumturquoise;
        const QColor mediumvioletred;
        const QColor midnightblue;
        const QColor mintcream;
        const QColor mistyrose;
        const QColor moccasin;
        const QColor navajowhite;
        const QColor navy;
        const QColor oldlace;
        const QColor olive;
        const QColor olivedrab;
        const QColor orange;
        const QColor orangered;
        const QColor orchid;
        const QColor palegoldenrod;
        const QColor palegreen;
        const QColor paleturquoise;
        const QColor palevioletred;
        const QColor papayawhip;
        const QColor peachpuff;
        const QColor peru;
        const QColor pink;
        const QColor plum;
        const QColor powderblue;
        const QColor purple;
        const QColor red;
        const QColor rosybrown;
        const QColor royalblue;
        const QColor saddlebrown;
        const QColor salmon;
        const QColor sandybrown;
        const QColor seagreen;
        const QColor seashell;
        const QColor sienna;
        const QColor silver;
        const QColor skyblue;
        const QColor slateblue;
        const QColor slategray;
        const QColor slategrey;
        const QColor snow;
        const QColor springgreen;
        const QColor steelblue;
        const QColor tan;
        const QColor teal;
        const QColor thistle;
        const QColor tomato;
        const QColor turquoise;
        const QColor violet;
        const QColor wheat;
        const QColor white;
        const QColor whitesmoke;
        const QColor yellow;
        const QColor yellowgreen;
    };
};
