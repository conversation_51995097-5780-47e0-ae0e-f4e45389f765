// qtextoption.sip generated by MetaSIP
//
// This file is part of the QtGui Python extension module.
//
// Copyright (c) 2025 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt6.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QTextOption
{
%TypeHeaderCode
#include <qtextoption.h>
%End

public:
    QTextOption();
    QTextOption(Qt::Alignment alignment);
    ~QTextOption();
    QTextOption(const QTextOption &o);
    Qt::Alignment alignment() const;
    void setTextDirection(Qt::LayoutDirection aDirection);
    Qt::LayoutDirection textDirection() const;

    enum WrapMode
    {
        NoWrap,
        WordWrap,
        ManualWrap,
        WrapAnywhere,
        WrapAtWordBoundaryOrAnywhere,
    };

    void setWrapMode(QTextOption::WrapMode wrap);
    QTextOption::WrapMode wrapMode() const;

    enum Flag /BaseType=Flag/
    {
        IncludeTrailingSpaces,
        ShowTabsAndSpaces,
        ShowLineAndParagraphSeparators,
        AddSpaceForLineAndParagraphSeparators,
        SuppressColors,
        ShowDocumentTerminator,
%If (Qt_6_9_0 -)
        ShowDefaultIgnorables,
%End
%If (Qt_6_9_0 -)
        DisableEmojiParsing,
%End
    };

    typedef QFlags<QTextOption::Flag> Flags;
    QTextOption::Flags flags() const;
    void setTabArray(const QList<qreal> &tabStops);
    QList<qreal> tabArray() const;
    void setUseDesignMetrics(bool b);
    bool useDesignMetrics() const;
    void setAlignment(Qt::Alignment aalignment);
    void setFlags(QTextOption::Flags flags);

    enum TabType
    {
        LeftTab,
        RightTab,
        CenterTab,
        DelimiterTab,
    };

    struct Tab
    {
%TypeHeaderCode
#include <qtextoption.h>
%End

        Tab();
        Tab(qreal pos, QTextOption::TabType tabType, QChar delim = QChar());
        bool operator==(const QTextOption::Tab &other) const;
        bool operator!=(const QTextOption::Tab &other) const;
        qreal position;
        QTextOption::TabType type;
        QChar delimiter;
    };

    void setTabs(const QList<QTextOption::Tab> &tabStops);
    QList<QTextOption::Tab> tabs() const;
    void setTabStopDistance(qreal tabStopDistance);
    qreal tabStopDistance() const;
};
