// qsessionmanager.sip generated by MetaSIP
//
// This file is part of the QtGui Python extension module.
//
// Copyright (c) 2025 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt6.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


%If (PyQt_SessionManager)

class QSessionManager : public QObject
{
%TypeHeaderCode
#include <qsessionmanager.h>
%End

    QSessionManager(QGuiApplication *app /TransferThis/, QString &id, QString &key);
    virtual ~QSessionManager();

public:
    QString sessionId() const;
    QString sessionKey() const;
    bool allowsInteraction();
    bool allowsErrorInteraction();
    void release();
    void cancel();

    enum RestartHint
    {
        RestartIfRunning,
        RestartAnyway,
        RestartImmediately,
        RestartNever,
    };

    void setRestartHint(QSessionManager::RestartHint);
    QSessionManager::RestartHint restartHint() const;
    void setRestartCommand(const QStringList &);
    QStringList restartCommand() const;
    void setDiscardCommand(const QStringList &);
    QStringList discardCommand() const;
    void setManagerProperty(const QString &name, const QString &value);
    void setManagerProperty(const QString &name, const QStringList &value);
    bool isPhase2() const;
    void requestPhase2();
};

%End
