// qvectornd.sip generated by MetaSIP
//
// This file is part of the QtGui Python extension module.
//
// Copyright (c) 2025 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt6.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


%ModuleCode
#include <qvectornd.h>
%End

class QVector2D
{
%TypeHeaderCode
#include <qvectornd.h>
%End

public:
    QVector2D();
    QVector2D(float xpos, float ypos);
    explicit QVector2D(QPoint point);
    explicit QVector2D(QPointF point);
    explicit QVector2D(QVector3D vector);
    explicit QVector2D(QVector4D vector);
    SIP_PYOBJECT __repr__() const /TypeHint="str"/;
%MethodCode
        PyObject *x = PyFloat_FromDouble(sipCpp->x());
        PyObject *y = PyFloat_FromDouble(sipCpp->y());
        
        if (x && y)
            sipRes = PyUnicode_FromFormat("PyQt6.QtGui.QVector2D(%R, %R)",
                    x, y);
        
        Py_XDECREF(x);
        Py_XDECREF(y);
%End

    bool isNull() const;
    float x() const;
    float y() const;
    void setX(float x);
    void setY(float y);
    float operator[](int i) const;
    float length() const;
    float lengthSquared() const;
    QVector2D normalized() const;
    void normalize();
    float distanceToPoint(QVector2D point) const;
    float distanceToLine(QVector2D point, QVector2D direction) const;
    QVector2D &operator+=(QVector2D vector);
    QVector2D &operator-=(QVector2D vector);
    QVector2D &operator*=(float factor);
    QVector2D &operator*=(QVector2D vector);
    QVector2D &operator/=(float divisor);
    QVector2D &operator/=(QVector2D vector);
    static float dotProduct(QVector2D v1, QVector2D v2);
    QVector3D toVector3D() const;
    QVector4D toVector4D() const;
    QPoint toPoint() const;
    QPointF toPointF() const;
};

class QVector3D
{
%TypeHeaderCode
#include <qvectornd.h>
%End

public:
    QVector3D();
    QVector3D(float xpos, float ypos, float zpos);
    explicit QVector3D(QPoint point);
    explicit QVector3D(QPointF point);
    QVector3D(QVector2D vector, float zpos);
%If (Qt_6_1_0 -)
    explicit QVector3D(QVector2D vector);
%End
%If (- Qt_6_1_0)
    QVector3D(QVector2D vector);
%End
    explicit QVector3D(QVector4D vector);
    SIP_PYOBJECT __repr__() const /TypeHint="str"/;
%MethodCode
        PyObject *x = PyFloat_FromDouble(sipCpp->x());
        PyObject *y = PyFloat_FromDouble(sipCpp->y());
        PyObject *z = PyFloat_FromDouble(sipCpp->z());
        
        if (x && y && z)
            sipRes = PyUnicode_FromFormat(
                    "PyQt6.QtGui.QVector3D(%R, %R, %R)", x, y, z);
        
        Py_XDECREF(x);
        Py_XDECREF(y);
        Py_XDECREF(z);
%End

    bool isNull() const;
    float x() const;
    float y() const;
    float z() const;
    void setX(float x);
    void setY(float y);
    void setZ(float z);
    float operator[](int i) const;
    float length() const;
    float lengthSquared() const;
    QVector3D normalized() const;
    void normalize();
    QVector3D &operator+=(QVector3D vector);
    QVector3D &operator-=(QVector3D vector);
    QVector3D &operator*=(float factor);
    QVector3D &operator*=(QVector3D vector);
    QVector3D &operator/=(float divisor);
    QVector3D &operator/=(QVector3D vector);
    static float dotProduct(QVector3D v1, QVector3D v2);
    static QVector3D crossProduct(QVector3D v1, QVector3D v2);
    static QVector3D normal(QVector3D v1, QVector3D v2);
    static QVector3D normal(QVector3D v1, QVector3D v2, QVector3D v3);
    QVector3D project(const QMatrix4x4 &modelView, const QMatrix4x4 &projection, const QRect &viewport) const;
    QVector3D unproject(const QMatrix4x4 &modelView, const QMatrix4x4 &projection, const QRect &viewport) const;
    float distanceToPoint(QVector3D point) const;
    float distanceToPlane(QVector3D plane, QVector3D normal) const;
    float distanceToPlane(QVector3D plane1, QVector3D plane2, QVector3D plane3) const;
    float distanceToLine(QVector3D point, QVector3D direction) const;
    QVector2D toVector2D() const;
    QVector4D toVector4D() const;
    QPoint toPoint() const;
    QPointF toPointF() const;
};

class QVector4D
{
%TypeHeaderCode
#include <qvectornd.h>
%End

public:
    QVector4D();
    QVector4D(float xpos, float ypos, float zpos, float wpos);
    explicit QVector4D(QPoint point);
    explicit QVector4D(QPointF point);
%If (Qt_6_1_0 -)
    explicit QVector4D(QVector2D vector);
%End
%If (- Qt_6_1_0)
    QVector4D(QVector2D vector);
%End
    QVector4D(QVector2D vector, float zpos, float wpos);
%If (Qt_6_1_0 -)
    explicit QVector4D(QVector3D vector);
%End
%If (- Qt_6_1_0)
    QVector4D(QVector3D vector);
%End
    QVector4D(QVector3D vector, float wpos);
    SIP_PYOBJECT __repr__() const /TypeHint="str"/;
%MethodCode
        PyObject *x = PyFloat_FromDouble(sipCpp->x());
        PyObject *y = PyFloat_FromDouble(sipCpp->y());
        PyObject *z = PyFloat_FromDouble(sipCpp->z());
        PyObject *w = PyFloat_FromDouble(sipCpp->w());
        
        if (x && y && z && w)
            sipRes = PyUnicode_FromFormat(
                    "PyQt6.QtGui.QVector4D(%R, %R, %R, %R)", x, y, z, w);
        
        Py_XDECREF(x);
        Py_XDECREF(y);
        Py_XDECREF(z);
        Py_XDECREF(w);
%End

    bool isNull() const;
    float x() const;
    float y() const;
    float z() const;
    float w() const;
    void setX(float x);
    void setY(float y);
    void setZ(float z);
    void setW(float w);
    float operator[](int i) const;
    float length() const;
    float lengthSquared() const;
    QVector4D normalized() const;
    void normalize();
    QVector4D &operator+=(QVector4D vector);
    QVector4D &operator-=(QVector4D vector);
    QVector4D &operator*=(float factor);
    QVector4D &operator*=(QVector4D vector);
    QVector4D &operator/=(float divisor);
    QVector4D &operator/=(QVector4D vector);
    static float dotProduct(QVector4D v1, QVector4D v2);
    QVector2D toVector2D() const;
    QVector2D toVector2DAffine() const;
    QVector3D toVector3D() const;
    QVector3D toVector3DAffine() const;
    QPoint toPoint() const;
    QPointF toPointF() const;
};

bool operator==(QVector4D v1, QVector4D v2);
bool operator==(QVector3D v1, QVector3D v2);
bool operator==(QVector2D v1, QVector2D v2);
bool operator!=(QVector4D v1, QVector4D v2);
bool operator!=(QVector3D v1, QVector3D v2);
bool operator!=(QVector2D v1, QVector2D v2);
QVector4D operator+(QVector4D v1, QVector4D v2);
QVector3D operator+(QVector3D v1, QVector3D v2);
QVector2D operator+(QVector2D v1, QVector2D v2);
QVector4D operator-(QVector4D vector);
QVector4D operator-(QVector4D v1, QVector4D v2);
QVector3D operator-(QVector3D vector);
QVector3D operator-(QVector3D v1, QVector3D v2);
QVector2D operator-(QVector2D vector);
QVector2D operator-(QVector2D v1, QVector2D v2);
QVector4D operator*(const QMatrix4x4 &matrix, const QVector4D &vector);
QVector4D operator*(const QVector4D &vector, const QMatrix4x4 &matrix);
QVector4D operator*(QVector4D v1, QVector4D v2);
QVector4D operator*(QVector4D vector, float factor);
QVector4D operator*(float factor, QVector4D vector);
QVector3D operator*(const QMatrix4x4 &matrix, const QVector3D &vector);
QVector3D operator*(const QVector3D &vector, const QMatrix4x4 &matrix);
QVector3D operator*(QVector3D v1, QVector3D v2);
QVector3D operator*(QVector3D vector, float factor);
QVector3D operator*(float factor, QVector3D vector);
QVector2D operator*(QVector2D v1, QVector2D v2);
QVector2D operator*(QVector2D vector, float factor);
QVector2D operator*(float factor, QVector2D vector);
QVector4D operator/(QVector4D vector, QVector4D divisor);
QVector4D operator/(QVector4D vector, float divisor);
QVector3D operator/(QVector3D vector, QVector3D divisor);
QVector3D operator/(QVector3D vector, float divisor);
QVector2D operator/(QVector2D vector, QVector2D divisor);
QVector2D operator/(QVector2D vector, float divisor);
bool qFuzzyCompare(QVector4D v1, QVector4D v2);
bool qFuzzyCompare(QVector3D v1, QVector3D v2);
bool qFuzzyCompare(QVector2D v1, QVector2D v2);
QDataStream &operator<<(QDataStream &, QVector4D) /ReleaseGIL/;
QDataStream &operator<<(QDataStream &, QVector3D) /ReleaseGIL/;
QDataStream &operator<<(QDataStream &, QVector2D) /ReleaseGIL/;
QDataStream &operator>>(QDataStream &, QVector4D & /Constrained/) /ReleaseGIL/;
QDataStream &operator>>(QDataStream &, QVector3D & /Constrained/) /ReleaseGIL/;
QDataStream &operator>>(QDataStream &, QVector2D & /Constrained/) /ReleaseGIL/;
