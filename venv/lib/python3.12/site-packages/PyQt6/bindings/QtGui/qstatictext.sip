// qstatictext.sip generated by MetaSIP
//
// This file is part of the QtGui Python extension module.
//
// Copyright (c) 2025 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt6.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QStaticText
{
%TypeHeaderCode
#include <qstatictext.h>
%End

public:
    enum PerformanceHint
    {
        ModerateCaching,
        AggressiveCaching,
    };

    QStaticText();
    explicit QStaticText(const QString &text);
    QStaticText(const QStaticText &other);
    ~QStaticText();
    void setText(const QString &text);
    QString text() const;
    void setTextFormat(Qt::TextFormat textFormat);
    Qt::TextFormat textFormat() const;
    void setTextWidth(qreal textWidth);
    qreal textWidth() const;
    void setTextOption(const QTextOption &textOption);
    QTextOption textOption() const;
    QSizeF size() const;
    void prepare(const QTransform &matrix = QTransform(), const QFont &font = QFont());
    void setPerformanceHint(QStaticText::PerformanceHint performanceHint);
    QStaticText::PerformanceHint performanceHint() const;
    bool operator==(const QStaticText &) const;
    bool operator!=(const QStaticText &) const;
    void swap(QStaticText &other /Constrained/);
};
