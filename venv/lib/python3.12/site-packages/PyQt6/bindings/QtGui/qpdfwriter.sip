// qpdfwriter.sip generated by MetaSIP
//
// This file is part of the QtGui Python extension module.
//
// Copyright (c) 2025 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt6.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QPdfWriter : public QObject, public QPagedPaintDevice
{
%TypeHeaderCode
#include <qpdfwriter.h>
%End

public:
    explicit QPdfWriter(const QString &filename);
    explicit QPdfWriter(QIODevice *device);
    virtual ~QPdfWriter();
    QString title() const;
    void setTitle(const QString &title);
    QString creator() const;
    void setCreator(const QString &creator);
    virtual bool newPage();

protected:
    virtual QPaintEngine *paintEngine() const;
    virtual int metric(QPaintDevice::PaintDeviceMetric id) const;

public:
    void setResolution(int resolution);
    int resolution() const;
    void setPdfVersion(QPagedPaintDevice::PdfVersion version);
    QPagedPaintDevice::PdfVersion pdfVersion() const;
    void setDocumentXmpMetadata(const QByteArray &xmpMetadata);
    QByteArray documentXmpMetadata() const;
    void addFileAttachment(const QString &fileName, const QByteArray &data, const QString &mimeType = QString());
%If (Qt_6_8_0 -)
    QUuid documentId() const;
%End
%If (Qt_6_8_0 -)
    void setDocumentId(QUuid documentId);
%End
%If (Qt_6_8_0 -)

    enum class ColorModel
    {
        RGB,
        Grayscale,
        CMYK,
        Auto,
    };

%End
%If (Qt_6_8_0 -)
    QPdfWriter::ColorModel colorModel() const;
%End
%If (Qt_6_8_0 -)
    void setColorModel(QPdfWriter::ColorModel model);
%End
%If (Qt_6_8_0 -)
    QPdfOutputIntent outputIntent() const;
%End
%If (Qt_6_8_0 -)
    void setOutputIntent(const QPdfOutputIntent &intent);
%End
%If (Qt_6_9_0 -)
    QString author() const;
%End
%If (Qt_6_9_0 -)
    void setAuthor(const QString &author);
%End
};
