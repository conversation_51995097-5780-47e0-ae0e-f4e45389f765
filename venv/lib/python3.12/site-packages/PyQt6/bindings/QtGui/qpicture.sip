// qpicture.sip generated by MetaSIP
//
// This file is part of the QtGui Python extension module.
//
// Copyright (c) 2025 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt6.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QPicture : public QPaintDevice
{
%TypeHeaderCode
#include <qpicture.h>
%End

public:
    explicit QPicture(int formatVersion = -1);
    QPicture(const QPicture &);
    virtual ~QPicture();
    bool isNull() const;
    virtual int devType() const;
    uint size() const;
    const char *data() const /Encoding="None"/;
    virtual void setData(const char *data /Array/, uint size /ArraySize/);
    bool play(QPainter *p);
    bool load(const QString &fileName) /ReleaseGIL/;
    bool load(QIODevice *dev) /ReleaseGIL/;
    bool save(const QString &fileName) /ReleaseGIL/;
    bool save(QIODevice *dev) /ReleaseGIL/;
    QRect boundingRect() const;
    void setBoundingRect(const QRect &r);
    void detach();
    bool isDetached() const;
    virtual QPaintEngine *paintEngine() const;

protected:
    virtual int metric(QPaintDevice::PaintDeviceMetric m) const;

public:
    void swap(QPicture &other /Constrained/);
};

QDataStream &operator<<(QDataStream &in, const QPicture &p) /ReleaseGIL/;
QDataStream &operator>>(QDataStream &in, QPicture &p /Constrained/) /ReleaseGIL/;
