// qsurfaceformat.sip generated by MetaSIP
//
// This file is part of the QtGui Python extension module.
//
// Copyright (c) 2025 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt6.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QSurfaceFormat
{
%TypeHeaderCode
#include <qsurfaceformat.h>
%End

public:
    enum FormatOption /BaseType=Flag/
    {
        StereoBuffers,
        DebugContext,
        DeprecatedFunctions,
        ResetNotification,
%If (Qt_6_2_0 -)
        ProtectedContent,
%End
    };

    typedef QFlags<QSurfaceFormat::FormatOption> FormatOptions;

    enum SwapBehavior
    {
        DefaultSwapBehavior,
        SingleBuffer,
        DoubleBuffer,
        TripleBuffer,
    };

    enum RenderableType
    {
        DefaultRenderableType,
        OpenGL,
        OpenGLES,
        OpenVG,
    };

    enum OpenGLContextProfile
    {
        NoProfile,
        CoreProfile,
        CompatibilityProfile,
    };

    QSurfaceFormat();
    QSurfaceFormat(QSurfaceFormat::FormatOptions options);
    QSurfaceFormat(const QSurfaceFormat &other);
    ~QSurfaceFormat();
    void setDepthBufferSize(int size);
    int depthBufferSize() const;
    void setStencilBufferSize(int size);
    int stencilBufferSize() const;
    void setRedBufferSize(int size);
    int redBufferSize() const;
    void setGreenBufferSize(int size);
    int greenBufferSize() const;
    void setBlueBufferSize(int size);
    int blueBufferSize() const;
    void setAlphaBufferSize(int size);
    int alphaBufferSize() const;
    void setSamples(int numSamples);
    int samples() const;
    void setSwapBehavior(QSurfaceFormat::SwapBehavior behavior);
    QSurfaceFormat::SwapBehavior swapBehavior() const;
    bool hasAlpha() const;
    void setProfile(QSurfaceFormat::OpenGLContextProfile profile);
    QSurfaceFormat::OpenGLContextProfile profile() const;
    void setRenderableType(QSurfaceFormat::RenderableType type);
    QSurfaceFormat::RenderableType renderableType() const;
    void setMajorVersion(int majorVersion);
    int majorVersion() const;
    void setMinorVersion(int minorVersion);
    int minorVersion() const;
    void setStereo(bool enable);
    bool stereo() const;
    std::pair<int, int> version() const;
    void setVersion(int major, int minor);
    void setOptions(QSurfaceFormat::FormatOptions options);
    void setOption(QSurfaceFormat::FormatOption option, bool on = true);
    bool testOption(QSurfaceFormat::FormatOption option) const;
    QSurfaceFormat::FormatOptions options() const;
    int swapInterval() const;
    void setSwapInterval(int interval);
    static void setDefaultFormat(const QSurfaceFormat &format);
    static QSurfaceFormat defaultFormat();
    const QColorSpace &colorSpace() const;
    void setColorSpace(const QColorSpace &colorSpace);
};

bool operator==(const QSurfaceFormat &, const QSurfaceFormat &);
bool operator!=(const QSurfaceFormat &, const QSurfaceFormat &);
