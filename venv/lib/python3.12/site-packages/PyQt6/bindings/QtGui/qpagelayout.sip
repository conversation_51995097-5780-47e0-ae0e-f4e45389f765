// qpagelayout.sip generated by MetaSIP
//
// This file is part of the QtGui Python extension module.
//
// Copyright (c) 2025 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt6.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QPageLayout
{
%TypeHeaderCode
#include <qpagelayout.h>
%End

public:
    enum Unit
    {
        Millimeter,
        Point,
        Inch,
        Pica,
        Didot,
        Cicero,
    };

    enum Orientation
    {
        Portrait,
        Landscape,
    };

    enum Mode
    {
        StandardMode,
        FullPageMode,
    };

%If (Qt_6_8_0 -)

    enum class OutOfBoundsPolicy
    {
        Reject,
        Clamp,
    };

%End
    QPageLayout();
    QPageLayout(const QPageSize &pageSize, QPageLayout::Orientation orientation, const QMarginsF &margins, QPageLayout::Unit units = QPageLayout::Point, const QMarginsF &minMargins = QMarginsF(0, 0, 0, 0));
    QPageLayout(const QPageLayout &other);
    ~QPageLayout();
    void swap(QPageLayout &other /Constrained/);
    bool isEquivalentTo(const QPageLayout &other) const;
    bool isValid() const;
    void setMode(QPageLayout::Mode mode);
    QPageLayout::Mode mode() const;
    void setPageSize(const QPageSize &pageSize, const QMarginsF &minMargins = QMarginsF(0, 0, 0, 0));
    QPageSize pageSize() const;
    void setOrientation(QPageLayout::Orientation orientation);
    QPageLayout::Orientation orientation() const;
    void setUnits(QPageLayout::Unit units);
    QPageLayout::Unit units() const;
%If (Qt_6_8_0 -)
    bool setMargins(const QMarginsF &margins, QPageLayout::OutOfBoundsPolicy outOfBoundsPolicy = QPageLayout::OutOfBoundsPolicy::Reject);
%End
%If (- Qt_6_8_0)
    bool setMargins(const QMarginsF &margins);
%End
%If (Qt_6_8_0 -)
    bool setLeftMargin(qreal leftMargin, QPageLayout::OutOfBoundsPolicy outOfBoundsPolicy = QPageLayout::OutOfBoundsPolicy::Reject);
%End
%If (- Qt_6_8_0)
    bool setLeftMargin(qreal leftMargin);
%End
%If (Qt_6_8_0 -)
    bool setRightMargin(qreal rightMargin, QPageLayout::OutOfBoundsPolicy outOfBoundsPolicy = QPageLayout::OutOfBoundsPolicy::Reject);
%End
%If (- Qt_6_8_0)
    bool setRightMargin(qreal rightMargin);
%End
%If (Qt_6_8_0 -)
    bool setTopMargin(qreal topMargin, QPageLayout::OutOfBoundsPolicy outOfBoundsPolicy = QPageLayout::OutOfBoundsPolicy::Reject);
%End
%If (- Qt_6_8_0)
    bool setTopMargin(qreal topMargin);
%End
%If (Qt_6_8_0 -)
    bool setBottomMargin(qreal bottomMargin, QPageLayout::OutOfBoundsPolicy outOfBoundsPolicy = QPageLayout::OutOfBoundsPolicy::Reject);
%End
%If (- Qt_6_8_0)
    bool setBottomMargin(qreal bottomMargin);
%End
    QMarginsF margins() const;
    QMarginsF margins(QPageLayout::Unit units) const;
    QMargins marginsPoints() const;
    QMargins marginsPixels(int resolution) const;
    void setMinimumMargins(const QMarginsF &minMargins);
    QMarginsF minimumMargins() const;
    QMarginsF maximumMargins() const;
    QRectF fullRect() const;
    QRectF fullRect(QPageLayout::Unit units) const;
    QRect fullRectPoints() const;
    QRect fullRectPixels(int resolution) const;
    QRectF paintRect() const;
    QRectF paintRect(QPageLayout::Unit units) const;
    QRect paintRectPoints() const;
    QRect paintRectPixels(int resolution) const;
};

bool operator==(const QPageLayout &lhs, const QPageLayout &rhs);
bool operator!=(const QPageLayout &lhs, const QPageLayout &rhs);
