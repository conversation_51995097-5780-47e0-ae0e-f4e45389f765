// qcommandlineparser.sip generated by MetaSIP
//
// This file is part of the QtCore Python extension module.
//
// Copyright (c) 2025 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt6.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QCommandLineParser
{
%TypeHeaderCode
#include <qcommandlineparser.h>
%End

public:
    QCommandLineParser();
    ~QCommandLineParser();

    enum SingleDashWordOptionMode
    {
        ParseAsCompactedShortOptions,
        ParseAsLongOptions,
    };

    void setSingleDashWordOptionMode(QCommandLineParser::SingleDashWordOptionMode parsingMode);
    bool addOption(const QCommandLineOption &commandLineOption);
    QCommandLineOption addVersionOption();
    QCommandLineOption addHelpOption();
    void setApplicationDescription(const QString &description);
    QString applicationDescription() const;
    void addPositionalArgument(const QString &name, const QString &description, const QString &syntax = QString());
    void clearPositionalArguments();
    void process(const QStringList &arguments) /ReleaseGIL/;
    void process(const QCoreApplication &app) /ReleaseGIL/;
    bool parse(const QStringList &arguments);
    QString errorText() const;
    bool isSet(const QString &name) const;
    QString value(const QString &name) const;
    QStringList values(const QString &name) const;
    bool isSet(const QCommandLineOption &option) const;
    QString value(const QCommandLineOption &option) const;
    QStringList values(const QCommandLineOption &option) const;
    QStringList positionalArguments() const;
    QStringList optionNames() const;
    QStringList unknownOptionNames() const;
    void showHelp(int exitCode = 0) /ReleaseGIL/;
    QString helpText() const;
    bool addOptions(const QList<QCommandLineOption> &options);
    void showVersion();

    enum OptionsAfterPositionalArgumentsMode
    {
        ParseAsOptions,
        ParseAsPositionalArguments,
    };

    void setOptionsAfterPositionalArgumentsMode(QCommandLineParser::OptionsAfterPositionalArgumentsMode mode);
%If (Qt_6_9_0 -)

    enum class MessageType
    {
        Information,
        Error,
    };

%End
%If (Qt_6_9_0 -)
    static void showMessageAndExit(QCommandLineParser::MessageType type, const QString &message, int exitCode = 0);
%End

private:
    QCommandLineParser(const QCommandLineParser &);
};
