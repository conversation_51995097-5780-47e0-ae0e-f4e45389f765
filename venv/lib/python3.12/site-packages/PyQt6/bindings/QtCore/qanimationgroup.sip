// qanimationgroup.sip generated by MetaSIP
//
// This file is part of the QtCore Python extension module.
//
// Copyright (c) 2025 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt6.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QAnimationGroup : public QAbstractAnimation
{
%TypeHeaderCode
#include <qanimationgroup.h>
%End

public:
    QAnimationGroup(QObject *parent /TransferThis/ = 0);
    virtual ~QAnimationGroup();
    QAbstractAnimation *animationAt(int index) const;
    int animationCount() const;
    int indexOfAnimation(QAbstractAnimation *animation) const;
    void addAnimation(QAbstractAnimation *animation /Transfer/);
    void insertAnimation(int index, QAbstractAnimation *animation /Transfer/);
    void removeAnimation(QAbstractAnimation *animation /TransferBack/);
    QAbstractAnimation *takeAnimation(int index) /TransferBack/;
    void clear();

protected:
    virtual bool event(QEvent *event);
};
