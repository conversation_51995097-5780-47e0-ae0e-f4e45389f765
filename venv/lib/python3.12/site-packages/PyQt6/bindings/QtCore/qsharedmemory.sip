// qsharedmemory.sip generated by MetaSIP
//
// This file is part of the QtCore Python extension module.
//
// Copyright (c) 2025 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt6.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QSharedMemory : public QObject
{
%TypeHeaderCode
#include <qsharedmemory.h>
%End

public:
    enum AccessMode
    {
        ReadOnly,
        ReadWrite,
    };

    enum SharedMemoryError
    {
        NoError,
        PermissionDenied,
        InvalidSize,
        KeyError,
        AlreadyExists,
        NotFound,
        LockError,
        OutOfResources,
        UnknownError,
    };

    QSharedMemory(QObject *parent /TransferThis/ = 0);
%If (Qt_6_6_0 -)
    QSharedMemory(const QNativeIpcKey &key, QObject *parent /TransferThis/ = 0);
%End
    QSharedMemory(const QString &key, QObject *parent /TransferThis/ = 0);
    virtual ~QSharedMemory();
    void setKey(const QString &key);
    QString key() const;
    bool create(qsizetype size, QSharedMemory::AccessMode mode = QSharedMemory::ReadWrite);
    qsizetype size() const;
    bool attach(QSharedMemory::AccessMode mode = QSharedMemory::ReadWrite);
    bool isAttached() const;
    bool detach();
    SIP_PYOBJECT data() /TypeHint="PyQt6.sip.voidptr"/;
%MethodCode
        sipRes = sipConvertFromVoidPtrAndSize(sipCpp->data(), sipCpp->size());
%End

    SIP_PYOBJECT constData() const /TypeHint="PyQt6.sip.voidptr"/;
%MethodCode
        sipRes = sipConvertFromConstVoidPtrAndSize(sipCpp->constData(), sipCpp->size());
%End

    bool lock();
    bool unlock();
    QSharedMemory::SharedMemoryError error() const;
    QString errorString() const;
%If (Qt_6_6_0 -)
    void setNativeKey(const QNativeIpcKey &key);
%End
%If (Qt_6_6_0 -)
    void setNativeKey(const QString &key, QNativeIpcKey::Type type = QNativeIpcKey::legacyDefaultTypeForOs());
%End
%If (- Qt_6_6_0)
    void setNativeKey(const QString &key);
%End
    QString nativeKey() const;
%If (Qt_6_6_0 -)
    QNativeIpcKey nativeIpcKey() const;
%End
%If (Qt_6_6_0 -)
    static bool isKeyTypeSupported(QNativeIpcKey::Type type);
%End
%If (Qt_6_6_0 -)
    static QNativeIpcKey platformSafeKey(const QString &key, QNativeIpcKey::Type type = QNativeIpcKey::DefaultTypeForOs);
%End
%If (Qt_6_6_0 -)
    static QNativeIpcKey legacyNativeKey(const QString &key, QNativeIpcKey::Type type = QNativeIpcKey::legacyDefaultTypeForOs());
%End
};
