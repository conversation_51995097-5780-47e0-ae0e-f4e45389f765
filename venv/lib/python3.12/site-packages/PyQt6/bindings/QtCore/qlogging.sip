// qlogging.sip generated by MetaSIP
//
// This file is part of the QtCore Python extension module.
//
// Copyright (c) 2025 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt6.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


%ModuleCode
#include <qlogging.h>
%End

enum QtMsgType
{
    QtDebugMsg,
    QtWarningMsg,
    QtCriticalMsg,
    QtFatalMsg,
    QtSystemMsg,
    QtInfoMsg,
};

class QMessageLogContext /NoDefaultCtors/
{
%TypeHeaderCode
#include <qlogging.h>
%End

public:
%If (Qt_6_8_0 -)
    static const int CurrentVersion;
%End
    int line;
    const char *file;
    const char *function;
    const char *category;
};

class QMessageLogger
{
%TypeHeaderCode
#include <qlogging.h>
%End

public:
    QMessageLogger();
    QMessageLogger(const char *file, int line, const char *function);
    QMessageLogger(const char *file, int line, const char *function, const char *category);
    void debug(const char *msg) const /ReleaseGIL/;
%MethodCode
        Py_BEGIN_ALLOW_THREADS
        sipCpp->debug("%s", a0);
        Py_END_ALLOW_THREADS
%End

    void debug(const QLoggingCategory &cat, const char *msg) const /ReleaseGIL/;
%MethodCode
        Py_BEGIN_ALLOW_THREADS
        sipCpp->debug(*a0, "%s", a1);
        Py_END_ALLOW_THREADS
%End

    void info(const char *msg) const /ReleaseGIL/;
%MethodCode
        Py_BEGIN_ALLOW_THREADS
        sipCpp->info("%s", a0);
        Py_END_ALLOW_THREADS
%End

    void info(const QLoggingCategory &cat, const char *msg) const /ReleaseGIL/;
%MethodCode
        Py_BEGIN_ALLOW_THREADS
        sipCpp->info(*a0, "%s", a1);
        Py_END_ALLOW_THREADS
%End

    void warning(const char *msg) const /ReleaseGIL/;
%MethodCode
        Py_BEGIN_ALLOW_THREADS
        sipCpp->warning("%s", a0);
        Py_END_ALLOW_THREADS
%End

    void warning(const QLoggingCategory &cat, const char *msg) const /ReleaseGIL/;
%MethodCode
        Py_BEGIN_ALLOW_THREADS
        sipCpp->warning(*a0, "%s", a1);
        Py_END_ALLOW_THREADS
%End

    void critical(const char *msg) const /ReleaseGIL/;
%MethodCode
        Py_BEGIN_ALLOW_THREADS
        sipCpp->critical("%s", a0);
        Py_END_ALLOW_THREADS
%End

    void critical(const QLoggingCategory &cat, const char *msg) const /ReleaseGIL/;
%MethodCode
        Py_BEGIN_ALLOW_THREADS
        sipCpp->critical(*a0, "%s", a1);
        Py_END_ALLOW_THREADS
%End

    void fatal(const char *msg) const /ReleaseGIL/;
%MethodCode
        Py_BEGIN_ALLOW_THREADS
        sipCpp->fatal("%s", a0);
        Py_END_ALLOW_THREADS
%End

%If (Qt_6_5_0 -)
    void fatal(const QLoggingCategory &cat, const char *msg) const /ReleaseGIL/;
%MethodCode
        Py_BEGIN_ALLOW_THREADS
        sipCpp->fatal(*a0, "%s", a1);
        Py_END_ALLOW_THREADS
%End

%End

private:
    QMessageLogger(const QMessageLogger &);
};

void qCritical(const char *msg) /ReleaseGIL/;
%MethodCode
    const char *file, *function;
    int line = qpycore_current_context(&file, &function);
    
    Py_BEGIN_ALLOW_THREADS
    QMessageLogger(file, line, function).critical("%s", a0);
    Py_END_ALLOW_THREADS
%End

void qDebug(const char *msg) /ReleaseGIL/;
%MethodCode
    const char *file, *function;
    int line = qpycore_current_context(&file, &function);
    
    Py_BEGIN_ALLOW_THREADS
    QMessageLogger(file, line, function).debug("%s", a0);
    Py_END_ALLOW_THREADS
%End

void qFatal(const char *msg) /ReleaseGIL/;
%MethodCode
    const char *file, *function;
    int line = qpycore_current_context(&file, &function);
    
    Py_BEGIN_ALLOW_THREADS
    QMessageLogger(file, line, function).fatal("%s", a0);
    Py_END_ALLOW_THREADS
%End

void qInfo(const char *msg) /ReleaseGIL/;
%MethodCode
    const char *file, *function;
    int line = qpycore_current_context(&file, &function);
    
    Py_BEGIN_ALLOW_THREADS
    QMessageLogger(file, line, function).info("%s", a0);
    Py_END_ALLOW_THREADS
%End

void qWarning(const char *msg) /ReleaseGIL/;
%MethodCode
    const char *file, *function;
    int line = qpycore_current_context(&file, &function);
    
    Py_BEGIN_ALLOW_THREADS
    QMessageLogger(file, line, function).warning("%s", a0);
    Py_END_ALLOW_THREADS
%End

SIP_PYCALLABLE qInstallMessageHandler(SIP_PYCALLABLE /AllowNone,TypeHint="Optional[Callable[[QtMsgType, QMessageLogContext, QString], None]]"/) /TypeHint="Optional[Callable[[QtMsgType, QMessageLogContext, QString], None]]"/;
%MethodCode
    // Treat None as the default handler.
    QtMessageHandler old = qInstallMessageHandler((a0 != Py_None) ? qtcore_MessageHandler : 0);
    
    // If we recognise the old handler, then return it.  Otherwise return
    // the default handler.  This doesn't exactly mimic the Qt behaviour
    // but it is probably close enough for the way it will be used.
    sipRes = (old == qtcore_MessageHandler) ? qtcore_PyMessageHandler : Py_None;
    Py_INCREF(sipRes);
    
    // Save the new Python handler.
    Py_XDECREF(qtcore_PyMessageHandler);
    qtcore_PyMessageHandler = a0;
    Py_INCREF(qtcore_PyMessageHandler);
%End

// Module code needed by qInstallMessageHandler().
%ModuleCode
// The user supplied Python handler.
static PyObject *qtcore_PyMessageHandler = 0;

// The C++ wrapper around the Python handler.
static void qtcore_MessageHandler(QtMsgType type, const QMessageLogContext &context, const QString &msg)
{
    PyObject *res;

    SIP_BLOCK_THREADS
    
    res = sipCallMethod(0, qtcore_PyMessageHandler, "FDD", type, sipType_QtMsgType, &context, sipType_QMessageLogContext, NULL, &msg, sipType_QString, NULL);

    Py_XDECREF(res);

    if (res != NULL && res != Py_None)
    {
        PyErr_SetString(PyExc_TypeError, "invalid result type from PyQt message handler");
        res = NULL;
    }

    if (res == NULL)
        pyqt6_err_print();

    SIP_UNBLOCK_THREADS
}
%End
void qSetMessagePattern(const QString &messagePattern);
QString qFormatLogMessage(QtMsgType type, const QMessageLogContext &context, const QString &buf);
