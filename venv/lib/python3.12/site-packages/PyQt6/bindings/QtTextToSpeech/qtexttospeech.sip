// qtexttospeech.sip generated by MetaSIP
//
// This file is part of the QtTextToSpeech Python extension module.
//
// Copyright (c) 2025 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt6.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QTextToSpeech : public QObject
{
%TypeHeaderCode
#include <qtexttospeech.h>
%End

%ConvertToSubClassCode
    static struct class_graph {
        const char *name;
        sipTypeDef **type;
        int yes, no;
    } graph[] = {
        {sipName_QTextToSpeech, &sipType_QTextToSpeech, -1, -1},
    };
    
    int i = 0;
    
    sipType = NULL;
    
    do
    {
        struct class_graph *cg = &graph[i];
    
        if (cg->name != NULL && sipCpp->inherits(cg->name))
        {
            sipType = *cg->type;
            i = cg->yes;
        }
        else
            i = cg->no;
    }
    while (i >= 0);
%End

public:
    enum class BoundaryHint
    {
        Default,
        Immediate,
        Word,
        Sentence,
%If (Qt_6_6_0 -)
        Utterance,
%End
    };

    enum class ErrorReason
    {
        NoError,
        Initialization,
        Configuration,
        Input,
        Playback,
    };

    enum State
    {
        Ready,
        Speaking,
        Paused,
        Error,
%If (Qt_6_6_0 -)
        Synthesizing,
%End
    };

    QTextToSpeech(const QString &engine, const QVariantMap &params, QObject *parent /TransferThis/ = 0);
    explicit QTextToSpeech(QObject *parent /TransferThis/ = 0);
    QTextToSpeech(const QString &engine, QObject *parent /TransferThis/ = 0);
    virtual ~QTextToSpeech();
    bool setEngine(const QString &engine, const QVariantMap &params = QVariantMap());
    QString engine() const;
    QTextToSpeech::ErrorReason errorReason() const;
    QString errorString() const;
    QTextToSpeech::State state() const;
    QList<QLocale> availableLocales() const;
    QLocale locale() const;
    QVoice voice() const;
    QList<QVoice> availableVoices() const;
    double rate() const;
    double pitch() const;
    double volume() const;
    static QStringList availableEngines();

public slots:
    void say(const QString &text);
    void stop(QTextToSpeech::BoundaryHint boundaryHint = QTextToSpeech::BoundaryHint::Default);
    void pause(QTextToSpeech::BoundaryHint boundaryHint = QTextToSpeech::BoundaryHint::Default);
    void resume();
    void setLocale(const QLocale &locale);
    void setRate(double rate);
    void setPitch(double pitch);
    void setVolume(double volume);
    void setVoice(const QVoice &voice);

signals:
    void stateChanged(QTextToSpeech::State state);
    void localeChanged(const QLocale &locale);
    void rateChanged(double rate);
    void pitchChanged(double pitch);
    void volumeChanged(double volume);
    void voiceChanged(const QVoice &voice);
    void engineChanged(const QString &engine);
    void errorOccurred(QTextToSpeech::ErrorReason error, const QString &errorString);

public:
%If (Qt_6_6_0 -)

    enum class Capability
    {
        None,
        Speak,
        PauseResume,
        WordByWordProgress,
        Synthesize,
    };

%End
%If (Qt_6_6_0 -)
    typedef QFlags<QTextToSpeech::Capability> Capabilities;
%End
%If (Qt_6_6_0 -)
    QTextToSpeech::Capabilities engineCapabilities() const;
%End

public slots:
%If (Qt_6_6_0 -)
    qsizetype enqueue(const QString &text);
%End

signals:
%If (Qt_6_6_0 -)
    void sayingWord(const QString &word, qsizetype id, qsizetype start, qsizetype length);
%End
%If (Qt_6_6_0 -)
    void aboutToSynthesize(qsizetype id);
%End
};
