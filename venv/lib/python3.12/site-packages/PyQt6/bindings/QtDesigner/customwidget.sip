// customwidget.sip generated by MetaSIP
//
// This file is part of the QtDesigner Python extension module.
//
// Copyright (c) 2025 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt6.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QDesignerCustomWidgetInterface
{
%TypeHeaderCode
#include <QtDesigner>
%End

public:
    virtual ~QDesignerCustomWidgetInterface();
    virtual QString name() const = 0;
    virtual QString group() const = 0;
    virtual QString toolTip() const = 0;
    virtual QString whatsThis() const = 0;
    virtual QString includeFile() const = 0;
    virtual QIcon icon() const = 0;
    virtual bool isContainer() const = 0;
    virtual QWidget *createWidget(QWidget *parent /TransferThis/) = 0 /Factory/;
    virtual bool isInitialized() const;
    virtual void initialize(QDesignerFormEditorInterface *core);
    virtual QString domXml() const;
    virtual QString codeTemplate() const;
};

class QDesignerCustomWidgetCollectionInterface
{
%TypeHeaderCode
#include <QtDesigner>
%End

public:
    virtual ~QDesignerCustomWidgetCollectionInterface();
    virtual QList<QDesignerCustomWidgetInterface*> customWidgets() const = 0;
};
