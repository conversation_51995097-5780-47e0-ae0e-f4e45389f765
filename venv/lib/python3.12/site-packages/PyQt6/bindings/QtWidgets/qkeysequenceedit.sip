// qkeysequenceedit.sip generated by MetaSIP
//
// This file is part of the QtWidgets Python extension module.
//
// Copyright (c) 2025 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt6.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QKeySequenceEdit : public QWidget
{
%TypeHeaderCode
#include <qkeysequenceedit.h>
%End

public:
    explicit QKeySequenceEdit(QWidget *parent /TransferThis/ = 0);
    QKeySequenceEdit(const QKeySequence &keySequence, QWidget *parent /TransferThis/ = 0);
    virtual ~QKeySequenceEdit();
    QKeySequence keySequence() const;

public slots:
    void setKeySequence(const QKeySequence &keySequence);
    void clear();

signals:
    void editingFinished();
    void keySequenceChanged(const QKeySequence &keySequence);

protected:
    virtual bool event(QEvent *);
    virtual void keyPressEvent(QKeyEvent *);
    virtual void keyReleaseEvent(QKeyEvent *);
    virtual void timerEvent(QTimerEvent *);
%If (Qt_6_4_0 -)
    virtual void focusOutEvent(QFocusEvent *);
%End

public:
%If (Qt_6_4_0 -)
    void setClearButtonEnabled(bool enable);
%End
%If (Qt_6_4_0 -)
    bool isClearButtonEnabled() const;
%End
%If (Qt_6_5_0 -)
    qsizetype maximumSequenceLength() const;
%End
%If (Qt_6_5_0 -)
    void setFinishingKeyCombinations(const QList<QKeyCombination> &finishingKeyCombinations);
%End
%If (Qt_6_5_0 -)
    QList<QKeyCombination> finishingKeyCombinations() const;
%End

public slots:
%If (Qt_6_5_0 -)
    void setMaximumSequenceLength(qsizetype count);
%End
};
