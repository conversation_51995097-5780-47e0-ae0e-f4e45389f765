// qabstractspinbox.sip generated by MetaSIP
//
// This file is part of the QtWidgets Python extension module.
//
// Copyright (c) 2025 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt6.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QAbstractSpinBox : public QWidget
{
%TypeHeaderCode
#include <qabstractspinbox.h>
%End

public:
    explicit QAbstractSpinBox(QWidget *parent /TransferThis/ = 0);
    virtual ~QAbstractSpinBox();

    enum StepEnabledFlag /BaseType=Flag/
    {
        StepNone,
        StepUpEnabled,
        StepDownEnabled,
    };

    typedef QFlags<QAbstractSpinBox::StepEnabledFlag> StepEnabled;

    enum ButtonSymbols
    {
        UpDownArrows,
        PlusMinus,
        NoButtons,
    };

    QAbstractSpinBox::ButtonSymbols buttonSymbols() const;
    void setButtonSymbols(QAbstractSpinBox::ButtonSymbols bs);
    QString text() const;
    QString specialValueText() const;
    void setSpecialValueText(const QString &s);
    bool wrapping() const;
    void setWrapping(bool w);
    void setReadOnly(bool r);
    bool isReadOnly() const;
    void setAlignment(Qt::Alignment flag);
    Qt::Alignment alignment() const;
    void setFrame(bool);
    bool hasFrame() const;
    virtual QSize sizeHint() const;
    virtual QSize minimumSizeHint() const;
    void interpretText();
    virtual bool event(QEvent *event);
    virtual QValidator::State validate(QString &input /In,Out/, int &pos /In,Out/) const;
    virtual void fixup(QString &input /In,Out/) const;
    virtual void stepBy(int steps);

public slots:
    void stepUp();
    void stepDown();
    void selectAll();
    virtual void clear();

signals:
    void editingFinished();

protected:
    virtual void resizeEvent(QResizeEvent *e);
    virtual void keyPressEvent(QKeyEvent *e);
    virtual void keyReleaseEvent(QKeyEvent *e);
    virtual void wheelEvent(QWheelEvent *e);
    virtual void focusInEvent(QFocusEvent *e);
    virtual void focusOutEvent(QFocusEvent *e);
    virtual void contextMenuEvent(QContextMenuEvent *e);
    virtual void changeEvent(QEvent *e);
    virtual void closeEvent(QCloseEvent *e);
    virtual void hideEvent(QHideEvent *e);
    virtual void mousePressEvent(QMouseEvent *e);
    virtual void mouseReleaseEvent(QMouseEvent *e);
    virtual void mouseMoveEvent(QMouseEvent *e);
    virtual void timerEvent(QTimerEvent *e);
    virtual void paintEvent(QPaintEvent *e);
    virtual void showEvent(QShowEvent *e);
    QLineEdit *lineEdit() const;
    void setLineEdit(QLineEdit *e /Transfer/);
    virtual QAbstractSpinBox::StepEnabled stepEnabled() const;
    virtual void initStyleOption(QStyleOptionSpinBox *option) const;

public:
    enum CorrectionMode
    {
        CorrectToPreviousValue,
        CorrectToNearestValue,
    };

    void setCorrectionMode(QAbstractSpinBox::CorrectionMode cm);
    QAbstractSpinBox::CorrectionMode correctionMode() const;
    bool hasAcceptableInput() const;
    void setAccelerated(bool on);
    bool isAccelerated() const;
    void setKeyboardTracking(bool kt);
    bool keyboardTracking() const;
    virtual QVariant inputMethodQuery(Qt::InputMethodQuery) const;
    void setGroupSeparatorShown(bool shown);
    bool isGroupSeparatorShown() const;

    enum StepType
    {
        DefaultStepType,
        AdaptiveDecimalStepType,
    };
};
