// qbluetoothdeviceinfo.sip generated by MetaSIP
//
// This file is part of the QtBluetooth Python extension module.
//
// Copyright (c) 2025 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt6.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


%If (Qt_6_2_0 -)

class QBluetoothDeviceInfo
{
%TypeHeaderCode
#include <qbluetoothdeviceinfo.h>
%End

public:
    enum MajorDeviceClass
    {
        MiscellaneousDevice,
        ComputerDevice,
        PhoneDevice,
        NetworkDevice,
        AudioVideoDevice,
        PeripheralDevice,
        ImagingDevice,
        WearableDevice,
        ToyDevice,
        HealthDevice,
        UncategorizedDevice,
    };

    enum MinorMiscellaneousClass
    {
        UncategorizedMiscellaneous,
    };

    enum MinorComputerClass
    {
        UncategorizedComputer,
        DesktopComputer,
        ServerComputer,
        LaptopComputer,
        HandheldClamShellComputer,
        HandheldComputer,
        WearableComputer,
    };

    enum MinorPhoneClass
    {
        UncategorizedPhone,
        CellularPhone,
        CordlessPhone,
        SmartPhone,
        WiredModemOrVoiceGatewayPhone,
        CommonIsdnAccessPhone,
    };

    enum MinorNetworkClass
    {
        NetworkFullService,
        NetworkLoadFactorOne,
        NetworkLoadFactorTwo,
        NetworkLoadFactorThree,
        NetworkLoadFactorFour,
        NetworkLoadFactorFive,
        NetworkLoadFactorSix,
        NetworkNoService,
    };

    enum MinorAudioVideoClass
    {
        UncategorizedAudioVideoDevice,
        WearableHeadsetDevice,
        HandsFreeDevice,
        Microphone,
        Loudspeaker,
        Headphones,
        PortableAudioDevice,
        CarAudio,
        SetTopBox,
        HiFiAudioDevice,
        Vcr,
        VideoCamera,
        Camcorder,
        VideoMonitor,
        VideoDisplayAndLoudspeaker,
        VideoConferencing,
        GamingDevice,
    };

    enum MinorPeripheralClass
    {
        UncategorizedPeripheral,
        KeyboardPeripheral,
        PointingDevicePeripheral,
        KeyboardWithPointingDevicePeripheral,
        JoystickPeripheral,
        GamepadPeripheral,
        RemoteControlPeripheral,
        SensingDevicePeripheral,
        DigitizerTabletPeripheral,
        CardReaderPeripheral,
    };

    enum MinorImagingClass
    {
        UncategorizedImagingDevice,
        ImageDisplay,
        ImageCamera,
        ImageScanner,
        ImagePrinter,
    };

    enum MinorWearableClass
    {
        UncategorizedWearableDevice,
        WearableWristWatch,
        WearablePager,
        WearableJacket,
        WearableHelmet,
        WearableGlasses,
    };

    enum MinorToyClass
    {
        UncategorizedToy,
        ToyRobot,
        ToyVehicle,
        ToyDoll,
        ToyController,
        ToyGame,
    };

    enum MinorHealthClass
    {
        UncategorizedHealthDevice,
        HealthBloodPressureMonitor,
        HealthThermometer,
        HealthWeightScale,
        HealthGlucoseMeter,
        HealthPulseOximeter,
        HealthDataDisplay,
        HealthStepCounter,
    };

    enum ServiceClass /BaseType=Flag/
    {
        NoService,
        PositioningService,
        NetworkingService,
        RenderingService,
        CapturingService,
        ObjectTransferService,
        AudioService,
        TelephonyService,
        InformationService,
        AllServices,
    };

    typedef QFlags<QBluetoothDeviceInfo::ServiceClass> ServiceClasses;
    QBluetoothDeviceInfo();
    QBluetoothDeviceInfo(const QBluetoothAddress &address, const QString &name, quint32 classOfDevice);
    QBluetoothDeviceInfo(const QBluetoothUuid &uuid, const QString &name, quint32 classOfDevice);
    QBluetoothDeviceInfo(const QBluetoothDeviceInfo &other);
    ~QBluetoothDeviceInfo();
    bool isValid() const;
    bool isCached() const;
    void setCached(bool cached);
    QBluetoothAddress address() const;
    QString name() const;
    QBluetoothDeviceInfo::ServiceClasses serviceClasses() const;
    QBluetoothDeviceInfo::MajorDeviceClass majorDeviceClass() const;
    quint8 minorDeviceClass() const;
    qint16 rssi() const;
    void setRssi(qint16 signal);
    void setServiceUuids(const QList<QBluetoothUuid> &uuids);
    QList<QBluetoothUuid> serviceUuids() const;

    enum CoreConfiguration /BaseType=Flag/
    {
        UnknownCoreConfiguration,
        LowEnergyCoreConfiguration,
        BaseRateCoreConfiguration,
        BaseRateAndLowEnergyCoreConfiguration,
    };

    typedef QFlags<QBluetoothDeviceInfo::CoreConfiguration> CoreConfigurations;
    void setCoreConfigurations(QBluetoothDeviceInfo::CoreConfigurations coreConfigs);
    QBluetoothDeviceInfo::CoreConfigurations coreConfigurations() const;
    void setDeviceUuid(const QBluetoothUuid &uuid);
    QBluetoothUuid deviceUuid() const;

    enum class Field /BaseType=Flag/
    {
        None,
        RSSI,
        ManufacturerData,
%If (Qt_6_3_0 -)
        ServiceData,
%End
        All,
    };

    typedef QFlags<QBluetoothDeviceInfo::Field> Fields;
    QList<unsigned short> manufacturerIds() const;
    QMultiHash<quint16, QByteArray> manufacturerData() const;
    QByteArray manufacturerData(quint16 manufacturerId) const;
    bool setManufacturerData(quint16 manufacturerId, const QByteArray &data);
    void setName(const QString &name);
%If (Qt_6_3_0 -)
    QList<QBluetoothUuid> serviceIds() const;
%End
%If (Qt_6_3_0 -)
    QMultiHash<QBluetoothUuid, QByteArray> serviceData() const;
%End
%If (Qt_6_3_0 -)
    QByteArray serviceData(const QBluetoothUuid &serviceId) const;
%End
%If (Qt_6_3_0 -)
    bool setServiceData(const QBluetoothUuid &serviceId, const QByteArray &data);
%End
};

%End
%If (Qt_6_2_0 -)
bool operator==(const QBluetoothDeviceInfo &a, const QBluetoothDeviceInfo &b);
%End
%If (Qt_6_2_0 -)
bool operator!=(const QBluetoothDeviceInfo &a, const QBluetoothDeviceInfo &b);
%End
