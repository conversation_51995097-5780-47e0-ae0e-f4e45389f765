// QtBluetoothmod.sip generated by MetaSIP
//
// This file is part of the QtBluetooth Python extension module.
//
// Copyright (c) 2025 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt6.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTAB<PERSON>ITY AND FITNESS FOR A PARTICULAR PURPOSE.


%Module(name=PyQt6.QtBluetooth, keyword_arguments="Optional", use_limited_api=True)

%Import QtCore/QtCoremod.sip

%Copying
Copyright (c) 2025 Riverbank Computing Limited <<EMAIL>>

This file is part of PyQt6.

This file may be used under the terms of the GNU General Public License
version 3.0 as published by the Free Software Foundation and appearing in
the file LICENSE included in the packaging of this file.  Please review the
following information to ensure the GNU General Public License version 3.0
requirements will be met: http://www.gnu.org/copyleft/gpl.html.

If you do not wish to use this file under the terms of the GPL version 3.0
then you may purchase a commercial license.  For more information contact
<EMAIL>.

This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.
%End

%DefaultSupertype PyQt6.sip.simplewrapper

%Include qbluetooth.sip
%Include qbluetoothaddress.sip
%Include qbluetoothdevicediscoveryagent.sip
%Include qbluetoothdeviceinfo.sip
%Include qbluetoothhostinfo.sip
%Include qbluetoothlocaldevice.sip
%Include qbluetoothserver.sip
%Include qbluetoothservicediscoveryagent.sip
%Include qbluetoothserviceinfo.sip
%Include qbluetoothsocket.sip
%Include qbluetoothuuid.sip
%Include qlowenergyadvertisingdata.sip
%Include qlowenergyadvertisingparameters.sip
%Include qlowenergycharacteristic.sip
%Include qlowenergycharacteristicdata.sip
%Include qlowenergyconnectionparameters.sip
%Include qlowenergycontroller.sip
%Include qlowenergydescriptor.sip
%Include qlowenergydescriptordata.sip
%Include qlowenergyservice.sip
%Include qlowenergyservicedata.sip
%Include qpybluetooth_quint128.sip
%Include qpybluetooth_qlist.sip
%Include qpybluetooth_qmultihash.sip
