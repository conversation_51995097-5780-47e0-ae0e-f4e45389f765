// qopenglwidget.sip generated by MetaSIP
//
// This file is part of the QtOpenGLWidgets Python extension module.
//
// Copyright (c) 2025 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt6.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QOpenGLWidget : public QWidget
{
%TypeHeaderCode
#include <qopenglwidget.h>
%End

%ConvertToSubClassCode
    static struct class_graph {
        const char *name;
        sipTypeDef **type;
        int yes, no;
    } graph[] = {
        {sipName_QOpenGLWidget, &sipType_QOpenGLWidget, -1, -1},
    };
    
    int i = 0;
    
    sipType = NULL;
    
    do
    {
        struct class_graph *cg = &graph[i];
    
        if (cg->name != NULL && sipCpp->inherits(cg->name))
        {
            sipType = *cg->type;
            i = cg->yes;
        }
        else
            i = cg->no;
    }
    while (i >= 0);
%End

public:
    QOpenGLWidget(QWidget *parent /TransferThis/ = 0, Qt::WindowFlags flags = Qt::WindowFlags());
    virtual ~QOpenGLWidget();
    void setFormat(const QSurfaceFormat &format);
    QSurfaceFormat format() const;
    bool isValid() const;
    void makeCurrent();
%If (Qt_6_5_0 -)
    void makeCurrent(QOpenGLWidget::TargetBuffer targetBuffer);
%End
    void doneCurrent();
    QOpenGLContext *context() const;
    GLuint defaultFramebufferObject() const;
%If (Qt_6_5_0 -)
    GLuint defaultFramebufferObject(QOpenGLWidget::TargetBuffer targetBuffer) const;
%End
    QImage grabFramebuffer();
%If (Qt_6_5_0 -)
    QImage grabFramebuffer(QOpenGLWidget::TargetBuffer targetBuffer);
%End

signals:
    void aboutToCompose();
    void frameSwapped();
    void aboutToResize();
    void resized();

protected:
    virtual void initializeGL();
    virtual void resizeGL(int w, int h);
    virtual void paintGL();
    virtual void paintEvent(QPaintEvent *e);
    virtual void resizeEvent(QResizeEvent *e);
    virtual bool event(QEvent *e);
    virtual int metric(QPaintDevice::PaintDeviceMetric metric) const;
    virtual QPaintEngine *paintEngine() const;

public:
    enum UpdateBehavior
    {
        NoPartialUpdate,
        PartialUpdate,
    };

    void setUpdateBehavior(QOpenGLWidget::UpdateBehavior updateBehavior);
    QOpenGLWidget::UpdateBehavior updateBehavior() const;
    GLenum textureFormat() const;
    void setTextureFormat(GLenum texFormat);
%If (Qt_6_5_0 -)

    enum TargetBuffer
    {
        LeftBuffer,
        RightBuffer,
    };

%End
%If (Qt_6_5_0 -)
    QOpenGLWidget::TargetBuffer currentTargetBuffer() const;
%End
};
