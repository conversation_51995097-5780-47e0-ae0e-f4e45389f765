// qnetworkreply.sip generated by MetaSIP
//
// This file is part of the QtNetwork Python extension module.
//
// Copyright (c) 2025 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt6.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QNetworkReply : public QIODevice
{
%TypeHeaderCode
#include <qnetworkreply.h>
%End

public:
    enum NetworkError
    {
        NoError,
        ConnectionRefusedError,
        RemoteHostClosedError,
        HostNotFoundError,
        TimeoutError,
        OperationCanceledError,
        SslHandshakeFailedError,
        UnknownNetworkError,
        ProxyConnectionRefusedError,
        ProxyConnectionClosedError,
        ProxyNotFoundError,
        ProxyTimeoutError,
        ProxyAuthenticationRequiredError,
        UnknownProxyError,
        ContentAccessDenied,
        ContentOperationNotPermittedError,
        ContentNotFoundError,
        AuthenticationRequiredError,
        UnknownContentError,
        ProtocolUnknownError,
        ProtocolInvalidOperationError,
        ProtocolFailure,
        ContentReSendError,
        TemporaryNetworkFailureError,
        NetworkSessionFailedError,
        BackgroundRequestNotAllowedError,
        ContentConflictError,
        ContentGoneError,
        InternalServerError,
        OperationNotImplementedError,
        ServiceUnavailableError,
        UnknownServerError,
        TooManyRedirectsError,
        InsecureRedirectError,
    };

    virtual ~QNetworkReply();
    virtual void abort() = 0;
    virtual void close();
    virtual bool isSequential() const;
    qint64 readBufferSize() const;
    virtual void setReadBufferSize(qint64 size);
    QNetworkAccessManager *manager() const;
    QNetworkAccessManager::Operation operation() const;
    QNetworkRequest request() const;
    QNetworkReply::NetworkError error() const;
    QUrl url() const;
    QVariant header(QNetworkRequest::KnownHeaders header) const;
%If (Qt_6_7_0 -)
    bool hasRawHeader(QAnyStringView headerName) const;
%End
%If (- Qt_6_7_0)
    bool hasRawHeader(const QByteArray &headerName) const;
%End
    QList<QByteArray> rawHeaderList() const;
%If (Qt_6_7_0 -)
    QByteArray rawHeader(QAnyStringView headerName) const;
%End
%If (- Qt_6_7_0)
    QByteArray rawHeader(const QByteArray &headerName) const;
%End
    QVariant attribute(QNetworkRequest::Attribute code) const;
%If (PyQt_SSL)
    QSslConfiguration sslConfiguration() const;
%End
%If (PyQt_SSL)
    void setSslConfiguration(const QSslConfiguration &configuration);
%End

public slots:
    virtual void ignoreSslErrors();

signals:
    void metaDataChanged();
    void finished();
%If (PyQt_SSL)
    void encrypted();
%End
    void errorOccurred(QNetworkReply::NetworkError);
%If (PyQt_SSL)
    void sslErrors(const QList<QSslError> &errors);
%End
    void uploadProgress(qint64 bytesSent, qint64 bytesTotal);
    void downloadProgress(qint64 bytesReceived, qint64 bytesTotal);
%If (PyQt_SSL)
    void preSharedKeyAuthenticationRequired(QSslPreSharedKeyAuthenticator *authenticator);
%End
    void redirected(const QUrl &url);
    void redirectAllowed();

protected:
    explicit QNetworkReply(QObject *parent /TransferThis/ = 0);
    virtual qint64 writeData(SIP_PYBUFFER) /ReleaseGIL/ [qint64 (const char *data, qint64 len)];
%MethodCode
        sipBufferInfoDef bi;
        
        if (sipGetBufferInfo(a0, &bi) > 0)
        {
            Py_BEGIN_ALLOW_THREADS
        #if defined(SIP_PROTECTED_IS_PUBLIC)
            sipRes = sipSelfWasArg ?
                    sipCpp->QNetworkReply::writeData(reinterpret_cast<char *>(bi.bi_buf), bi.bi_len) :
                    sipCpp->writeData(reinterpret_cast<char *>(bi.bi_buf), bi.bi_len);
        #else
            sipRes = sipCpp->sipProtectVirt_writeData(sipSelfWasArg, reinterpret_cast<char *>(bi.bi_buf),
                    bi.bi_len);
        #endif
            Py_END_ALLOW_THREADS
            
            sipReleaseBufferInfo(&bi);
        }
        else
        {
            sipIsErr = 1;
        }
%End

    void setOperation(QNetworkAccessManager::Operation operation);
    void setRequest(const QNetworkRequest &request);
    void setError(QNetworkReply::NetworkError errorCode, const QString &errorString);
    void setUrl(const QUrl &url);
    void setHeader(QNetworkRequest::KnownHeaders header, const QVariant &value);
    void setRawHeader(const QByteArray &headerName, const QByteArray &value);
    void setAttribute(QNetworkRequest::Attribute code, const QVariant &value);
    void setFinished(bool finished);

public:
    bool isFinished() const;
    bool isRunning() const;
%If (PyQt_SSL)
    void ignoreSslErrors(const QList<QSslError> &errors);
%End
    typedef std::pair<QByteArray, QByteArray> RawHeaderPair;
    const QList<std::pair<QByteArray, QByteArray>> &rawHeaderPairs() const;

protected:
%If (PyQt_SSL)
    virtual void sslConfigurationImplementation(QSslConfiguration &) const;
%End
%If (PyQt_SSL)
    virtual void setSslConfigurationImplementation(const QSslConfiguration &);
%End
%If (PyQt_SSL)
    virtual void ignoreSslErrorsImplementation(const QList<QSslError> &);
%End

signals:
%If (Qt_6_3_0 -)
    void socketStartedConnecting();
%End
%If (Qt_6_3_0 -)
    void requestSent();
%End

public:
%If (Qt_6_8_0 -)
    QHttpHeaders headers() const;
%End

protected:
%If (Qt_6_8_0 -)
    void setHeaders(const QHttpHeaders &newHeaders);
%End
%If (Qt_6_8_0 -)
    void setWellKnownHeader(QHttpHeaders::WellKnownHeader name, QByteArrayView value);
%End
};
