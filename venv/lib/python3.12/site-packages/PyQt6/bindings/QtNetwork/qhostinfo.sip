// qhostinfo.sip generated by MetaSIP
//
// This file is part of the QtNetwork Python extension module.
//
// Copyright (c) 2025 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt6.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICEN<PERSON> included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QHostInfo
{
%TypeHeaderCode
#include <qhostinfo.h>
%End

public:
    enum HostInfoError
    {
        NoE<PERSON><PERSON>,
        HostNotFound,
        UnknownError,
    };

    explicit QHostInfo(int id = -1);
    QHostInfo(const QHostInfo &d);
    ~QHostInfo();
    QString hostName() const;
    void setHostName(const QString &name);
    QList<QHostAddress> addresses() const;
    void setAddresses(const QList<QHostAddress> &addresses);
    QHostInfo::HostInfoError error() const;
    void setError(QHostInfo::HostInfoError error);
    QString errorString() const;
    void setErrorString(const QString &errorString);
    void setLookupId(int id);
    int lookupId() const;
    static int lookupHost(const QString &name, SIP_PYOBJECT slot /TypeHint="PYQT_SLOT"/);
%MethodCode
        QObject *receiver;
        QByteArray slot_signature;
        
        if ((sipError = pyqt6_qtnetwork_get_connection_parts(a1, 0, "(QHostInfo)", true, &receiver, slot_signature)) == sipErrorNone)
        {
            QHostInfo::lookupHost(*a0, receiver, slot_signature.constData());
        }
        else if (sipError == sipErrorContinue)
        {
            sipError = sipBadCallableArg(1, a1);
        }
%End

    static void abortHostLookup(int lookupId);
    static QHostInfo fromName(const QString &name);
    static QString localHostName();
    static QString localDomainName();
    void swap(QHostInfo &other /Constrained/);
};

%ModuleHeaderCode
// Imports from QtCore.
typedef sipErrorState (*pyqt6_qtnetwork_get_connection_parts_t)(PyObject *, QObject *, const char *, bool, QObject **, QByteArray &);
extern pyqt6_qtnetwork_get_connection_parts_t pyqt6_qtnetwork_get_connection_parts;
%End

%ModuleCode
// Imports from QtCore.
pyqt6_qtnetwork_get_connection_parts_t pyqt6_qtnetwork_get_connection_parts;
%End

%PostInitialisationCode
// Imports from QtCore.
pyqt6_qtnetwork_get_connection_parts = (pyqt6_qtnetwork_get_connection_parts_t)sipImportSymbol("pyqt6_get_connection_parts");
Q_ASSERT(pyqt6_qtnetwork_get_connection_parts);
%End
