// qwebsocket.sip generated by MetaSIP
//
// This file is part of the QtWebSockets Python extension module.
//
// Copyright (c) 2025 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt6.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


%If (Qt_6_2_0 -)

class QWebSocket : public QObject
{
%TypeHeaderCode
#include <qwebsocket.h>
%End

%ConvertToSubClassCode
    static struct class_graph {
        const char *name;
        sipTypeDef **type;
        int yes, no;
    } graph[] = {
        {sipName_QWebSocket, &sipType_QWebSocket, -1, 1},
        {sipName_QWebSocketServer, &sipType_QWebSocketServer, -1, 2},
        {sipName_QMaskGenerator, &sipType_QMaskGenerator, -1, -1},
    };
    
    int i = 0;
    
    sipType = NULL;
    
    do
    {
        struct class_graph *cg = &graph[i];
    
        if (cg->name != NULL && sipCpp->inherits(cg->name))
        {
            sipType = *cg->type;
            i = cg->yes;
        }
        else
            i = cg->no;
    }
    while (i >= 0);
%End

public:
    QWebSocket(const QString &origin = QString(), QWebSocketProtocol::Version version = QWebSocketProtocol::VersionLatest, QObject *parent /TransferThis/ = 0);
    virtual ~QWebSocket();
    void abort();
    QAbstractSocket::SocketError error() const;
    QString errorString() const;
    bool flush() /ReleaseGIL/;
    bool isValid() const;
    QHostAddress localAddress() const;
    quint16 localPort() const;
    QAbstractSocket::PauseModes pauseMode() const;
    QHostAddress peerAddress() const;
    QString peerName() const;
    quint16 peerPort() const;
    QNetworkProxy proxy() const;
    void setProxy(const QNetworkProxy &networkProxy);
    void setMaskGenerator(const QMaskGenerator *maskGenerator /KeepReference/);
    const QMaskGenerator *maskGenerator() const;
    qint64 readBufferSize() const;
    void setReadBufferSize(qint64 size);
    void resume() /ReleaseGIL/;
    void setPauseMode(QAbstractSocket::PauseModes pauseMode);
    QAbstractSocket::SocketState state() const;
    QWebSocketProtocol::Version version() const;
    QString resourceName() const;
    QUrl requestUrl() const;
    QString origin() const;
    QWebSocketProtocol::CloseCode closeCode() const;
    QString closeReason() const;
    qint64 sendTextMessage(const QString &message) /ReleaseGIL/;
    qint64 sendBinaryMessage(const QByteArray &data) /ReleaseGIL/;
%If (PyQt_SSL)
    void ignoreSslErrors(const QList<QSslError> &errors);
%End
%If (PyQt_SSL)
    void setSslConfiguration(const QSslConfiguration &sslConfiguration);
%End
%If (PyQt_SSL)
    QSslConfiguration sslConfiguration() const;
%End
    QNetworkRequest request() const;

public slots:
    void close(QWebSocketProtocol::CloseCode closeCode = QWebSocketProtocol::CloseCodeNormal, const QString &reason = QString()) /ReleaseGIL/;
%If (Qt_6_4_0 -)
    void open(const QNetworkRequest &request, const QWebSocketHandshakeOptions &options) /ReleaseGIL/;
%End
%If (Qt_6_4_0 -)
    void open(const QUrl &url, const QWebSocketHandshakeOptions &options) /ReleaseGIL/;
%End
    void open(const QUrl &url) /ReleaseGIL/;
    void open(const QNetworkRequest &request) /ReleaseGIL/;
    void ping(const QByteArray &payload = QByteArray()) /ReleaseGIL/;
%If (PyQt_SSL)
    void ignoreSslErrors();
%End

signals:
    void aboutToClose();
    void connected();
    void disconnected();
    void stateChanged(QAbstractSocket::SocketState state);
    void proxyAuthenticationRequired(const QNetworkProxy &proxy, QAuthenticator *pAuthenticator);
    void readChannelFinished();
    void textFrameReceived(const QString &frame, bool isLastFrame);
    void binaryFrameReceived(const QByteArray &frame, bool isLastFrame);
    void textMessageReceived(const QString &message);
    void binaryMessageReceived(const QByteArray &message);
    void error(QAbstractSocket::SocketError error);
    void pong(quint64 elapsedTime, const QByteArray &payload);
    void bytesWritten(qint64 bytes);
%If (PyQt_SSL)
    void sslErrors(const QList<QSslError> &errors);
%End
%If (PyQt_SSL)
    void preSharedKeyAuthenticationRequired(QSslPreSharedKeyAuthenticator *authenticator);
%End

public:
    qint64 bytesToWrite() const;
    void setMaxAllowedIncomingFrameSize(quint64 maxAllowedIncomingFrameSize);
    quint64 maxAllowedIncomingFrameSize() const;
    void setMaxAllowedIncomingMessageSize(quint64 maxAllowedIncomingMessageSize);
    quint64 maxAllowedIncomingMessageSize() const;
    static quint64 maxIncomingMessageSize();
    static quint64 maxIncomingFrameSize();
    void setOutgoingFrameSize(quint64 outgoingFrameSize);
    quint64 outgoingFrameSize() const;
    static quint64 maxOutgoingFrameSize();
    void continueInterruptedHandshake();

signals:
%If (PyQt_SSL)
    void peerVerifyError(const QSslError &error);
%End
%If (PyQt_SSL)
    void alertSent(QSsl::AlertLevel level, QSsl::AlertType type, const QString &description);
%End
%If (PyQt_SSL)
    void alertReceived(QSsl::AlertLevel level, QSsl::AlertType type, const QString &description);
%End
%If (PyQt_SSL)
    void handshakeInterruptedOnError(const QSslError &error);
%End

public:
%If (Qt_6_4_0 -)
    QWebSocketHandshakeOptions handshakeOptions() const;
%End
%If (Qt_6_4_0 -)
    QString subprotocol() const;
%End

signals:
%If (Qt_6_5_0 -)
    void errorOccurred(QAbstractSocket::SocketError error);
%End
%If (Qt_6_6_0 -)
    void authenticationRequired(QAuthenticator *authenticator);
%End
};

%End
