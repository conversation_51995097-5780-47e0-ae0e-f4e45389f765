// qjsvalue.sip generated by MetaSIP
//
// This file is part of the QtQml Python extension module.
//
// Copyright (c) 2025 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt6.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


typedef QList<QJSValue> QJSValueList;

class QJSValue /TypeHintIn="Union[QJSValue, QJSValue.SpecialValue, bool, int, float, QString]"/
{
%TypeHeaderCode
#include <qjsvalue.h>
%End

%ConvertToTypeCode
if (!sipIsErr)
    return qpyqml_canConvertTo_QJSValue(sipPy);

return qpyqml_convertTo_QJSValue(sipPy, sipTransferObj, sipCppPtr, sipIsErr);
%End

public:
    enum SpecialValue
    {
        NullValue,
        UndefinedValue,
    };

%If (Qt_6_1_0 -)

    enum ObjectConversionBehavior
    {
        ConvertJSObjects,
        RetainJSObjects,
    };

%End
    QJSValue(QJSValue::SpecialValue value /Constrained/ = QJSValue::UndefinedValue);
    QJSValue(const QJSValue &other);
    ~QJSValue();
    bool isBool() const;
    bool isNumber() const;
    bool isNull() const;
    bool isString() const;
    bool isUndefined() const;
    bool isVariant() const;
    bool isQObject() const;
    bool isObject() const;
    bool isDate() const;
    bool isRegExp() const;
    bool isArray() const;
    bool isError() const;
%If (Qt_6_2_0 -)
    bool isUrl() const;
%End
    QString toString() const;
    double toNumber() const;
    qint32 toInt() const;
    quint32 toUInt() const;
    bool toBool() const;
    QVariant toVariant() const;
%If (Qt_6_1_0 -)
    QVariant toVariant(QJSValue::ObjectConversionBehavior behavior) const;
%End
%If (Qt_6_1_0 -)
    QJSPrimitiveValue toPrimitive() const;
%End
    QObject *toQObject() const;
    QDateTime toDateTime() const;
    bool equals(const QJSValue &other) const;
    bool strictlyEquals(const QJSValue &other) const;
    QJSValue prototype() const;
    void setPrototype(const QJSValue &prototype);
    QJSValue property(const QString &name) const;
    void setProperty(const QString &name, const QJSValue &value);
    bool hasProperty(const QString &name) const;
    bool hasOwnProperty(const QString &name) const;
    QJSValue property(quint32 arrayIndex) const;
    void setProperty(quint32 arrayIndex, const QJSValue &value);
    bool deleteProperty(const QString &name);
    bool isCallable() const;
    QJSValue call(const QJSValueList &args = QJSValueList()) const;
    QJSValue callWithInstance(const QJSValue &instance, const QJSValueList &args = QJSValueList()) const;
    QJSValue callAsConstructor(const QJSValueList &args = QJSValueList()) const;

    enum ErrorType
    {
        GenericError,
        EvalError,
        RangeError,
        ReferenceError,
        SyntaxError,
        TypeError,
        URIError,
    };

    QJSValue::ErrorType errorType() const;
};

QDataStream &operator<<(QDataStream &, const QJSValue &) /ReleaseGIL/;
QDataStream &operator>>(QDataStream &, QJSValue & /Constrained/) /ReleaseGIL/;
