// qjsmanagedvalue.sip generated by MetaSIP
//
// This file is part of the QtQml Python extension module.
//
// Copyright (c) 2025 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt6.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


%If (Qt_6_1_0 -)

class QJSManagedValue /NoDefaultCtors/
{
%TypeHeaderCode
#include <qjsmanagedvalue.h>
%End

public:
    enum Type
    {
        Undefined,
        Boolean,
        Number,
        String,
        Object,
        Symbol,
        Function,
    };

    QJSManagedValue();
    QJSManagedValue(QJSValue value, QJSEngine *engine);
    QJSManagedValue(const QJSPrimitiveValue &value, QJSEngine *engine);
    QJSManagedValue(const QString &string, QJSEngine *engine);
    QJSManagedValue(const QVariant &variant, QJSEngine *engine);
    ~QJSManagedValue();
    bool equals(const QJSManagedValue &other) const;
    bool strictlyEquals(const QJSManagedValue &other) const;
    QJSEngine *engine() const;
    QJSManagedValue prototype() const;
    void setPrototype(const QJSManagedValue &prototype);
    QJSManagedValue::Type type() const;
    bool isUndefined() const;
    bool isBoolean() const;
    bool isNumber() const;
    bool isString() const;
    bool isObject() const;
    bool isSymbol() const;
    bool isFunction() const;
    bool isInteger() const;
    bool isNull() const;
    bool isRegularExpression() const;
    bool isArray() const;
    bool isUrl() const;
    bool isVariant() const;
    bool isQObject() const;
    bool isQMetaObject() const;
    bool isDate() const;
    bool isError() const;
    QString toString() const;
    double toNumber() const;
    bool toBoolean() const;
    QJSPrimitiveValue toPrimitive() const;
    QJSValue toJSValue() const;
    QVariant toVariant() const;
    int toInteger() const;
    QRegularExpression toRegularExpression() const;
    QUrl toUrl() const;
    QObject *toQObject() const;
    const QMetaObject *toQMetaObject() const;
    QDateTime toDateTime() const;
    bool hasProperty(const QString &name) const;
    bool hasOwnProperty(const QString &name) const;
    QJSValue property(const QString &name) const;
    void setProperty(const QString &name, const QJSValue &value);
    bool deleteProperty(const QString &name);
    bool hasProperty(quint32 arrayIndex) const;
    bool hasOwnProperty(quint32 arrayIndex) const;
    QJSValue property(quint32 arrayIndex) const;
    void setProperty(quint32 arrayIndex, const QJSValue &value);
    bool deleteProperty(quint32 arrayIndex);
    QJSValue call(const QJSValueList &arguments = {}) const;
    QJSValue callWithInstance(const QJSValue &instance, const QJSValueList &arguments = {}) const;
    QJSValue callAsConstructor(const QJSValueList &arguments = {}) const;
};

%End
