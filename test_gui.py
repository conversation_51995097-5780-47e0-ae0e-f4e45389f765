#!/usr/bin/env python3
"""
Test script to demonstrate the IDM Clone GUI functionality
"""

import sys
import os
import time
from PyQt6.QtWidgets import QApplication
from PyQt6.QtCore import QTimer

# Add current directory to path for imports
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from gui.main_window import MainWindow


def test_gui():
    """Test the GUI application"""
    print("🚀 Testing IDM Clone GUI Application")
    print("=" * 50)
    
    # Create QApplication
    app = QApplication(sys.argv)
    app.setApplicationName("IDM Clone Test")
    
    # Create main window
    window = MainWindow()
    window.show()
    
    print("✅ GUI application started successfully!")
    print("📋 Features available:")
    print("   • Add URL dialog (Ctrl+N or File menu)")
    print("   • Download list with progress bars")
    print("   • Context menu for download actions")
    print("   • Multi-threaded downloads")
    print("   • Real-time progress updates")
    print("   • Download management (start, pause, cancel)")
    print("   • File and folder operations")
    print("")
    print("🎯 To test:")
    print("   1. Click 'Add URL' or press Ctrl+N")
    print("   2. Enter a download URL (e.g., https://httpbin.org/bytes/1048576)")
    print("   3. Click OK to start download")
    print("   4. Watch progress in the download list")
    print("   5. Right-click downloads for more options")
    print("")
    print("⚠️  Close the window to exit the test")
    
    # Auto-close after 30 seconds for automated testing
    def auto_close():
        print("\n⏰ Auto-closing test application...")
        app.quit()
    
    timer = QTimer()
    timer.timeout.connect(auto_close)
    timer.start(30000)  # 30 seconds
    
    # Start event loop
    try:
        return app.exec()
    except KeyboardInterrupt:
        print("\n⚠️ Test interrupted by user")
        return 0


if __name__ == "__main__":
    exit_code = test_gui()
    print(f"\n✅ GUI test completed with exit code: {exit_code}")
    sys.exit(exit_code)
