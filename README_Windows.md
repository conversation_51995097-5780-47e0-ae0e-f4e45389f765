# 🪟 Windows Setup Guide for IDM-Style Download Server

## 📋 Prerequisites

### 1. Install Python

- Download Python from [python.org](https://python.org)
- **Important**: Check "Add Python to PATH" during installation
- Recommended: Python 3.8 or higher

### 2. Verify Installation

Open Command Prompt or PowerShell and run:

```cmd
python --version
pip --version
```

## 🚀 Quick Start (3 Methods)

### Method 1: One-Click Batch File (Recommended)

1. Download all files to a folder
2. Double-click `run_windows.bat`
3. Wait for setup to complete
4. Server starts automatically at `http://localhost:8000`

### Method 2: PowerShell Script

1. Right-click `run_windows_powershell.ps1`
2. Select "Run with PowerShell"
3. If prompted about execution policy, type `Y` and press Enter

### Method 3: Manual Setup

```cmd
# 1. Create virtual environment
python -m venv venv

# 2. Activate virtual environment
venv\Scripts\activate.bat

# 3. Install dependencies
pip install -r requirements.txt

# 4. Start server
python -m uvicorn main1:app --reload --host 0.0.0.0 --port 8000
```

## 🔧 Troubleshooting

### Common Issues:

#### 1. "Python is not recognized"

**Solution**:

- Reinstall Python and check "Add to PATH"
- Or add Python manually to PATH:
  1. Search "Environment Variables" in Windows
  2. Edit "Path" variable
  3. Add Python installation directory

#### 2. "pip is not recognized"

**Solution**:

- Python 3.4+ includes pip by default
- If missing, run: `python -m ensurepip --upgrade`

#### 3. PowerShell Execution Policy Error

**Solution**:

```powershell
Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope CurrentUser
```

#### 4. Port 8000 Already in Use

**Solution**:

- Change port in the command: `--port 8001`
- Or kill the process using port 8000:

```cmd
netstat -ano | findstr :8000
taskkill /PID <PID> /F
```

## 📡 API Usage

### 1. Download Files

```cmd
curl "http://localhost:8000/download?url=YOUR_FILE_URL"
```

### 2. Check Download Capabilities

```cmd
curl "http://localhost:8000/download-info?url=YOUR_FILE_URL"
```

### 3. Test Performance

```cmd
curl "http://localhost:8000/test-download"
```

## 🌐 Web Interface (Optional)

Add this to your browser:

```
http://localhost:8000/docs
```

This opens the interactive API documentation.

## 📁 File Structure

```
your-project/
├── main1.py                    # Main server code
├── requirements.txt            # Python dependencies
├── run_windows.bat            # Windows batch file
├── run_windows_powershell.ps1 # PowerShell script
├── README_Windows.md          # This guide
└── downloads/                 # Downloaded files folder
```

## ⚡ Features

- ✅ **Multi-threaded downloads** (IDM-style)
- ✅ **Auto-resume** capability
- ✅ **Range request** support
- ✅ **Google Drive** link handling
- ✅ **Progress tracking** per segment
- ✅ **Automatic fallback** to single-threaded
- ✅ **Windows-compatible** paths and commands

## 🔄 Updating

To update the server:

1. Stop the server (Ctrl+C)
2. Run the setup script again
3. Or manually: `pip install -r requirements.txt --upgrade`

## 🛡️ Security Notes

- Server runs on `0.0.0.0:8000` (accessible from network)
- For local-only access, change to `--host 127.0.0.1`
- Downloads are saved to `downloads/` folder
- Temporary files are automatically cleaned up

## 📞 Support

If you encounter issues:

1. Check Python version: `python --version`
2. Check pip version: `pip --version`
3. Verify virtual environment is activated (should see `(venv)` in prompt)
4. Check firewall settings if accessing from other devices
