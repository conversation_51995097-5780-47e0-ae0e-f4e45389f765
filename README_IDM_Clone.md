# 🚀 IDM Clone - Internet Download Manager Clone

A modern, cross-platform download manager with IDM-style features built with Python and PyQt6.

## ✨ Features

### Core Download Engine
- **Multi-threaded Downloads**: IDM-style segmented downloading for faster speeds
- **Range Request Support**: Automatic detection and utilization of server range support
- **Resume Capability**: Resume interrupted downloads
- **Google Drive Support**: Direct download from Google Drive sharing links
- **Smart Filename Detection**: Automatic filename extraction from URLs and headers
- **Progress Tracking**: Real-time progress updates with speed and ETA calculation

### User Interface
- **IDM-like Interface**: Familiar layout with download list, progress bars, and controls
- **Download List**: Table view showing filename, size, progress, speed, status, and segments
- **Add URL Dialog**: Clean interface for adding new downloads with validation
- **Context Menus**: Right-click actions for download management
- **Real-time Updates**: Live progress updates and status changes
- **Cross-platform**: Works on Ubuntu, Windows, and macOS

### Download Management
- **Queue Management**: Automatic download queue with configurable concurrent downloads
- **Download Controls**: Start, pause, resume, cancel, and retry downloads
- **Status Tracking**: Pending, downloading, paused, completed, failed, cancelled states
- **Error Handling**: Automatic retry with configurable retry limits
- **File Operations**: Open downloaded files and folders directly from the interface

## 🏗️ Architecture

```
idm_clone/
├── core/                   # Core download functionality
│   ├── download_engine.py  # Multi-threaded download engine
│   ├── download_manager.py # Download queue and management
│   ├── download_item.py    # Download data model
│   └── utils.py           # Utility functions
├── gui/                   # User interface
│   ├── main_window.py     # Main application window
│   ├── download_list.py   # Download list widget
│   ├── add_url_dialog.py  # Add URL dialog
│   └── styles.py          # UI styling
├── idm_clone.py          # Main application entry point
└── requirements.txt      # Dependencies
```

## 🚀 Quick Start

### Prerequisites
- Python 3.8 or higher
- PyQt6 (automatically installed)

### Installation

1. **Clone or download the project files**

2. **Install dependencies**:
   ```bash
   pip install -r requirements.txt
   ```

3. **Run the application**:
   ```bash
   python3 idm_clone.py
   ```

### Ubuntu/Linux Setup
```bash
# Install Python and pip if not already installed
sudo apt update
sudo apt install python3 python3-pip

# Install dependencies
pip3 install -r requirements.txt

# Run the application
python3 idm_clone.py
```

### Windows Setup
```cmd
# Install dependencies
pip install -r requirements.txt

# Run the application
python idm_clone.py
```

## 📖 Usage

### Adding Downloads
1. Click **"Add URL"** button or press `Ctrl+N`
2. Enter the download URL
3. Optionally customize the filename and download location
4. Click **"OK"** to add to queue

### Managing Downloads
- **Start**: Click start button or right-click → Start Download
- **Pause**: Click pause button or right-click → Pause Download
- **Resume**: Right-click paused download → Resume Download
- **Cancel**: Right-click → Cancel Download
- **Retry**: Right-click failed download → Retry Download
- **Remove**: Right-click → Remove from List

### File Operations
- **Open File**: Double-click completed download or right-click → Open File
- **Open Folder**: Right-click → Open Folder
- **Downloads Folder**: Tools menu → Open Downloads Folder

## 🧪 Testing

### Test Core Functionality
```bash
python3 simple_test.py
```

### Test GUI Application
```bash
python3 test_gui.py
```

### Test URLs for Development
- Small file: `https://httpbin.org/bytes/1048576` (1MB)
- Medium file: `https://httpbin.org/bytes/10485760` (10MB)
- Large file: Any public download link

## ⚙️ Configuration

### Download Settings
- **Max Concurrent Downloads**: 3 (configurable in DownloadManager)
- **Max Workers per Download**: 8 (configurable in DownloadEngine)
- **Chunk Size**: 1MB (configurable)
- **Segment Size**: 1MB - 10MB (auto-calculated)

### File Locations
- **Downloads**: `./downloads/` (default)
- **State File**: `./downloads_state.json` (auto-saved)
- **Temp Files**: System temp directory (auto-cleaned)

## 🔧 Development

### Project Structure
- **Core Module**: Download engine and management logic
- **GUI Module**: PyQt6 user interface components
- **Cross-platform**: Designed to work on Ubuntu, Windows, and macOS

### Key Classes
- `DownloadEngine`: Multi-threaded download implementation
- `DownloadManager`: Queue management and coordination
- `DownloadItem`: Data model for individual downloads
- `MainWindow`: Primary GUI application window
- `DownloadListWidget`: Download list display and interaction

### Threading Model
- **Main Thread**: GUI and user interaction
- **Queue Thread**: Download queue processing
- **Worker Threads**: Individual download execution
- **Progress Callbacks**: Thread-safe progress reporting

## 🐛 Troubleshooting

### Common Issues

1. **PyQt6 Import Error**:
   ```bash
   pip install PyQt6
   ```

2. **Permission Denied**:
   - Check download folder permissions
   - Run with appropriate user permissions

3. **Network Issues**:
   - Check internet connection
   - Verify URL accessibility
   - Check firewall settings

4. **GUI Not Displaying**:
   - Ensure X11 forwarding (if using SSH)
   - Check display environment variables
   - Verify PyQt6 installation

### Debug Mode
Enable debug output by setting environment variable:
```bash
export QT_LOGGING_RULES="*=true"
python3 idm_clone.py
```

## 🚀 Future Enhancements

### Planned Features
- **Browser Integration**: Browser extension for one-click downloads
- **Download Scheduling**: Schedule downloads for specific times
- **Bandwidth Limiting**: Control download speed limits
- **Categories**: Organize downloads into categories
- **Themes**: Dark mode and custom themes
- **Notifications**: Desktop notifications for completed downloads

### Windows Deployment
- **Executable Creation**: PyInstaller for standalone .exe
- **Installer**: NSIS installer for easy Windows deployment
- **Auto-updater**: Automatic update mechanism

## 📄 License

This project is open source and available under the MIT License.

## 🤝 Contributing

Contributions are welcome! Please feel free to submit pull requests or open issues for bugs and feature requests.

## 📞 Support

For support and questions:
1. Check the troubleshooting section
2. Review the test scripts for examples
3. Open an issue for bugs or feature requests

---

**Built with ❤️ using Python and PyQt6**
