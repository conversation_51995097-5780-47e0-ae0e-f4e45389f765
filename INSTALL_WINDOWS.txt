🚀 IDM-Style Download Server - Windows Installation Guide
================================================================

📦 What's Included:
- main1.py (Main server with IDM features)
- requirements.txt (Python dependencies)
- run_windows.bat (One-click setup)
- run_windows_powershell.ps1 (PowerShell setup)
- launch_gui.py (GUI launcher)
- README_Windows.md (Detailed guide)
- Windows_Setup_Complete.md (Complete reference)

📋 Prerequisites:
1. Python 3.8+ installed from python.org
2. Python added to PATH during installation
3. Internet connection

🚀 Quick Start (Choose One):

METHOD 1 - Easiest (Recommended):
1. Extract all files to a folder
2. Double-click "run_windows.bat"
3. Wait for setup to complete
4. Server starts at http://localhost:8000

METHOD 2 - PowerShell:
1. Extract all files to a folder
2. Right-click "run_windows_powershell.ps1"
3. Select "Run with PowerShell"
4. Type 'Y' if prompted about execution policy

METHOD 3 - GUI:
1. Extract all files to a folder
2. Run "run_windows.bat" first (for setup)
3. Then run: python launch_gui.py
4. Click "Start Server" in the GUI

METHOD 4 - Manual:
1. Extract all files to a folder
2. Open Command Prompt in the folder
3. Run: python -m venv venv
4. Run: venv\Scripts\activate.bat
5. Run: pip install -r requirements.txt
6. Run: python -m uvicorn main1:app --reload --host 0.0.0.0 --port 8000

🌐 Usage:
- Download files: http://localhost:8000/download?url=YOUR_URL
- Check capabilities: http://localhost:8000/download-info?url=YOUR_URL
- Test performance: http://localhost:8000/test-download
- API docs: http://localhost:8000/docs

⚡ Features:
- Multi-threaded downloads (IDM-style)
- Auto-resume capability
- Google Drive support
- Range request support
- Smart fallback system

🔧 Troubleshooting:
- If "Python not found": Reinstall Python with "Add to PATH" checked
- If port 8000 busy: Change port in command to --port 8001
- If PowerShell error: Run: Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope CurrentUser

📞 Support:
- Check README_Windows.md for detailed guide
- Check Windows_Setup_Complete.md for complete reference

🎉 Enjoy your IDM-style download server! 