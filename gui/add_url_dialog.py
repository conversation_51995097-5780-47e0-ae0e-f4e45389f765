"""
Add URL dialog for adding new downloads
"""

from PyQt6.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QFormLayout,
                            QLineEdit, QPushButton, QLabel, QCheckBox, 
                            QFileDialog, QMessageBox, QGroupBox)
from PyQt6.QtCore import Qt
from PyQt6.QtGui import QFont
import os
import re
from urllib.parse import urlparse


class AddUrlDialog(QDialog):
    """
    Dialog for adding new download URLs
    """
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setup_ui()
        
    def setup_ui(self):
        """Setup the user interface"""
        self.setWindowTitle("Add New Download")
        self.setModal(True)
        self.resize(500, 300)
        
        # Main layout
        layout = QVBoxLayout()
        
        # Title
        title_label = QLabel("Add New Download")
        title_font = QFont()
        title_font.setPointSize(14)
        title_font.setBold(True)
        title_label.setFont(title_font)
        title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(title_label)
        
        # URL section
        url_group = QGroupBox("Download URL")
        url_layout = QVBoxLayout()
        
        # URL input
        self.url_edit = QLineEdit()
        self.url_edit.setPlaceholderText("Enter download URL (http://, https://, ftp://)")
        self.url_edit.textChanged.connect(self.on_url_changed)
        url_layout.addWidget(self.url_edit)
        
        # URL validation label
        self.url_status_label = QLabel("")
        self.url_status_label.setStyleSheet("color: gray; font-size: 10px;")
        url_layout.addWidget(self.url_status_label)
        
        url_group.setLayout(url_layout)
        layout.addWidget(url_group)
        
        # File details section
        details_group = QGroupBox("File Details")
        details_layout = QFormLayout()
        
        # Filename
        self.filename_edit = QLineEdit()
        self.filename_edit.setPlaceholderText("Auto-detected from URL")
        details_layout.addRow("Filename:", self.filename_edit)
        
        # Download location
        location_layout = QHBoxLayout()
        self.location_edit = QLineEdit()
        self.location_edit.setText("downloads")
        self.location_edit.setReadOnly(True)
        
        browse_button = QPushButton("Browse...")
        browse_button.clicked.connect(self.browse_location)
        
        location_layout.addWidget(self.location_edit)
        location_layout.addWidget(browse_button)
        details_layout.addRow("Save to:", location_layout)
        
        details_group.setLayout(details_layout)
        layout.addWidget(details_group)
        
        # Options section
        options_group = QGroupBox("Options")
        options_layout = QVBoxLayout()
        
        self.start_immediately_check = QCheckBox("Start download immediately")
        self.start_immediately_check.setChecked(True)
        options_layout.addWidget(self.start_immediately_check)
        
        options_group.setLayout(options_layout)
        layout.addWidget(options_group)
        
        # Buttons
        button_layout = QHBoxLayout()
        
        self.ok_button = QPushButton("OK")
        self.ok_button.setDefault(True)
        self.ok_button.clicked.connect(self.accept)
        self.ok_button.setEnabled(False)
        
        cancel_button = QPushButton("Cancel")
        cancel_button.clicked.connect(self.reject)
        
        button_layout.addStretch()
        button_layout.addWidget(self.ok_button)
        button_layout.addWidget(cancel_button)
        
        layout.addLayout(button_layout)
        self.setLayout(layout)
        
        # Focus on URL input
        self.url_edit.setFocus()
        
    def on_url_changed(self, text: str):
        """Handle URL text changes"""
        if not text.strip():
            self.url_status_label.setText("")
            self.ok_button.setEnabled(False)
            self.filename_edit.clear()
            return
            
        # Validate URL
        if self.is_valid_url(text):
            self.url_status_label.setText("✓ Valid URL")
            self.url_status_label.setStyleSheet("color: green; font-size: 10px;")
            self.ok_button.setEnabled(True)
            
            # Try to extract filename
            filename = self.extract_filename_from_url(text)
            if filename:
                self.filename_edit.setPlaceholderText(filename)
            else:
                self.filename_edit.setPlaceholderText("Auto-detected from URL")
        else:
            self.url_status_label.setText("✗ Invalid URL")
            self.url_status_label.setStyleSheet("color: red; font-size: 10px;")
            self.ok_button.setEnabled(False)
            self.filename_edit.clear()
            
    def is_valid_url(self, url: str) -> bool:
        """Check if URL is valid"""
        try:
            result = urlparse(url)
            return all([result.scheme, result.netloc]) and result.scheme in ['http', 'https', 'ftp']
        except:
            return False
            
    def extract_filename_from_url(self, url: str) -> str:
        """Extract filename from URL"""
        try:
            parsed = urlparse(url)
            path = parsed.path
            
            # Remove query parameters and fragments
            if '?' in path:
                path = path.split('?')[0]
            if '#' in path:
                path = path.split('#')[0]
                
            filename = os.path.basename(path)
            
            # Clean up filename
            if filename and '.' in filename:
                # Remove invalid characters
                filename = re.sub(r'[<>:"/\\|?*]', '_', filename)
                return filename
                
        except:
            pass
            
        return ""
        
    def browse_location(self):
        """Browse for download location"""
        folder = QFileDialog.getExistingDirectory(
            self, 
            "Select Download Folder",
            self.location_edit.text()
        )
        
        if folder:
            self.location_edit.setText(folder)
            
    def get_url(self) -> str:
        """Get the entered URL"""
        return self.url_edit.text().strip()
        
    def get_filename(self) -> str:
        """Get the filename (custom or auto-detected)"""
        custom_filename = self.filename_edit.text().strip()
        if custom_filename:
            return custom_filename
            
        # Use placeholder text if available
        placeholder = self.filename_edit.placeholderText()
        if placeholder and placeholder != "Auto-detected from URL":
            return placeholder
            
        return ""
        
    def get_location(self) -> str:
        """Get the download location"""
        return self.location_edit.text().strip()
        
    def should_start_immediately(self) -> bool:
        """Check if download should start immediately"""
        return self.start_immediately_check.isChecked()
        
    def accept(self):
        """Handle dialog acceptance"""
        url = self.get_url()
        
        if not url:
            QMessageBox.warning(self, "Invalid Input", "Please enter a valid URL.")
            return
            
        if not self.is_valid_url(url):
            QMessageBox.warning(self, "Invalid URL", "Please enter a valid HTTP, HTTPS, or FTP URL.")
            return
            
        location = self.get_location()
        if not os.path.exists(location):
            reply = QMessageBox.question(
                self, 
                "Create Folder", 
                f"The folder '{location}' does not exist. Create it?",
                QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No
            )
            
            if reply == QMessageBox.StandardButton.Yes:
                try:
                    os.makedirs(location, exist_ok=True)
                except OSError as e:
                    QMessageBox.critical(self, "Error", f"Could not create folder: {e}")
                    return
            else:
                return
                
        super().accept()
        
    def keyPressEvent(self, event):
        """Handle key press events"""
        # Allow Enter to submit if URL is valid
        if event.key() in (Qt.Key.Key_Return, Qt.Key.Key_Enter):
            if self.ok_button.isEnabled():
                self.accept()
            return
            
        super().keyPressEvent(event)
