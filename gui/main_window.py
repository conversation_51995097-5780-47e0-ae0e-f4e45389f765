"""
Main window for IDM Clone application
"""

import sys
import os
from PyQt6.QtWidgets import (QApplication, QMainWindow, QVBoxLayout, QHBoxLayout,
                             QWidget, QMenuBar, QToolBar, QStatusBar, QSplitter,
                             QMessageBox, QFileDialog)
from PyQt6.QtCore import Qt, QTimer, pyqtSignal
from PyQt6.QtGui import QIcon, Q<PERSON>eySequence, QAction

from .download_list import DownloadListWidget
from .add_url_dialog import AddUrlDialog
from core.download_manager import DownloadManager
from core.download_engine import DownloadStatus, ProgressInfo


class MainWindow(QMainWindow):
    """
    Main application window with IDM-like interface
    """

    def __init__(self):
        super().__init__()
        self.download_manager = None
        self.setup_ui()
        self.setup_download_manager()
        self.setup_timer()

    def setup_ui(self):
        """Setup the user interface"""
        self.setWindowTitle("IDM Clone - Download Manager")
        self.setGeometry(100, 100, 1000, 600)

        # Create central widget
        central_widget = QWidget()
        self.setCentralWidget(central_widget)

        # Create main layout
        main_layout = QVBoxLayout(central_widget)
        main_layout.setContentsMargins(0, 0, 0, 0)

        # Setup menu bar
        self.setup_menu_bar()

        # Setup toolbar
        self.setup_toolbar()

        # Create download list
        self.download_list = DownloadListWidget()
        main_layout.addWidget(self.download_list)

        # Setup status bar
        self.setup_status_bar()

        # Connect signals
        self.download_list.download_action_requested.connect(
            self.handle_download_action)

    def setup_menu_bar(self):
        """Setup the menu bar"""
        menubar = self.menuBar()

        # File menu
        file_menu = menubar.addMenu('&File')

        add_url_action = QAction('&Add URL...', self)
        add_url_action.setShortcut(QKeySequence('Ctrl+N'))
        add_url_action.setStatusTip('Add new download URL')
        add_url_action.triggered.connect(self.show_add_url_dialog)
        file_menu.addAction(add_url_action)

        file_menu.addSeparator()

        exit_action = QAction('E&xit', self)
        exit_action.setShortcut(QKeySequence('Ctrl+Q'))
        exit_action.setStatusTip('Exit application')
        exit_action.triggered.connect(self.close)
        file_menu.addAction(exit_action)

        # Downloads menu
        downloads_menu = menubar.addMenu('&Downloads')

        start_all_action = QAction('&Start All', self)
        start_all_action.setStatusTip('Start all pending downloads')
        start_all_action.triggered.connect(self.start_all_downloads)
        downloads_menu.addAction(start_all_action)

        pause_all_action = QAction('&Pause All', self)
        pause_all_action.setStatusTip('Pause all active downloads')
        pause_all_action.triggered.connect(self.pause_all_downloads)
        downloads_menu.addAction(pause_all_action)

        downloads_menu.addSeparator()

        clear_completed_action = QAction('&Clear Completed', self)
        clear_completed_action.setStatusTip(
            'Remove completed downloads from list')
        clear_completed_action.triggered.connect(
            self.clear_completed_downloads)
        downloads_menu.addAction(clear_completed_action)

        # Tools menu
        tools_menu = menubar.addMenu('&Tools')

        open_downloads_folder_action = QAction('Open &Downloads Folder', self)
        open_downloads_folder_action.setStatusTip(
            'Open downloads folder in file manager')
        open_downloads_folder_action.triggered.connect(
            self.open_downloads_folder)
        tools_menu.addAction(open_downloads_folder_action)

        # Help menu
        help_menu = menubar.addMenu('&Help')

        about_action = QAction('&About', self)
        about_action.setStatusTip('About IDM Clone')
        about_action.triggered.connect(self.show_about)
        help_menu.addAction(about_action)

    def setup_toolbar(self):
        """Setup the toolbar"""
        toolbar = QToolBar()
        toolbar.setMovable(False)
        self.addToolBar(toolbar)

        # Add URL button
        add_url_action = QAction('Add URL', self)
        add_url_action.setStatusTip('Add new download URL')
        add_url_action.triggered.connect(self.show_add_url_dialog)
        toolbar.addAction(add_url_action)

        toolbar.addSeparator()

        # Start button
        start_action = QAction('Start', self)
        start_action.setStatusTip('Start selected download')
        start_action.triggered.connect(self.start_selected_download)
        toolbar.addAction(start_action)

        # Pause button
        pause_action = QAction('Pause', self)
        pause_action.setStatusTip('Pause selected download')
        pause_action.triggered.connect(self.pause_selected_download)
        toolbar.addAction(pause_action)

        # Cancel button
        cancel_action = QAction('Cancel', self)
        cancel_action.setStatusTip('Cancel selected download')
        cancel_action.triggered.connect(self.cancel_selected_download)
        toolbar.addAction(cancel_action)

        toolbar.addSeparator()

        # Remove button
        remove_action = QAction('Remove', self)
        remove_action.setStatusTip('Remove selected download')
        remove_action.triggered.connect(self.remove_selected_download)
        toolbar.addAction(remove_action)

    def setup_status_bar(self):
        """Setup the status bar"""
        self.status_bar = QStatusBar()
        self.setStatusBar(self.status_bar)
        self.status_bar.showMessage('Ready')

    def setup_download_manager(self):
        """Setup the download manager"""
        self.download_manager = DownloadManager(
            download_dir="downloads",
            max_concurrent_downloads=3,
            max_workers_per_download=8
        )

        # Connect callbacks
        self.download_manager.add_progress_callback(self.on_download_progress)
        self.download_manager.add_status_callback(
            self.on_download_status_changed)

        # Load previous state
        self.download_manager.load_state()

        # Update download list
        self.refresh_download_list()

    def setup_timer(self):
        """Setup timer for periodic updates"""
        self.update_timer = QTimer()
        self.update_timer.timeout.connect(self.update_ui)
        self.update_timer.start(1000)  # Update every second

    def show_add_url_dialog(self):
        """Show add URL dialog"""
        dialog = AddUrlDialog(self)
        if dialog.exec() == AddUrlDialog.DialogCode.Accepted:
            url = dialog.get_url()
            filename = dialog.get_filename()

            if url:
                download_id = self.download_manager.add_download(url, filename)
                self.refresh_download_list()
                self.status_bar.showMessage(
                    f'Added download: {filename or "Unknown"}')

    def refresh_download_list(self):
        """Refresh the download list"""
        downloads = self.download_manager.get_all_downloads()
        download_ids = list(self.download_manager.downloads.keys())
        self.download_list.update_downloads(downloads, download_ids)

    def handle_download_action(self, action: str, download_id: str):
        """Handle download actions from the list widget"""
        if action == "start":
            self.download_manager.start_download(download_id)
        elif action == "pause":
            self.download_manager.pause_download(download_id)
        elif action == "resume":
            self.download_manager.resume_download(download_id)
        elif action == "cancel":
            self.download_manager.cancel_download(download_id)
        elif action == "retry":
            self.download_manager.retry_download(download_id)
        elif action == "remove":
            self.download_manager.remove_download(download_id)
            self.refresh_download_list()
        elif action == "open_file":
            download_item = self.download_manager.get_download(download_id)
            if download_item and download_item.file_exists():
                self.open_file(download_item.get_file_path())
        elif action == "open_folder":
            download_item = self.download_manager.get_download(download_id)
            if download_item:
                self.open_folder(download_item.output_path)

    def start_selected_download(self):
        """Start selected download"""
        selected_id = self.download_list.get_selected_download_id()
        if selected_id:
            self.download_manager.start_download(selected_id)

    def pause_selected_download(self):
        """Pause selected download"""
        selected_id = self.download_list.get_selected_download_id()
        if selected_id:
            self.download_manager.pause_download(selected_id)

    def cancel_selected_download(self):
        """Cancel selected download"""
        selected_id = self.download_list.get_selected_download_id()
        if selected_id:
            self.download_manager.cancel_download(selected_id)

    def remove_selected_download(self):
        """Remove selected download"""
        selected_id = self.download_list.get_selected_download_id()
        if selected_id:
            reply = QMessageBox.question(self, 'Remove Download',
                                         'Are you sure you want to remove this download?',
                                         QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No)
            if reply == QMessageBox.StandardButton.Yes:
                self.download_manager.remove_download(selected_id)
                self.refresh_download_list()

    def start_all_downloads(self):
        """Start all pending downloads"""
        pending_downloads = self.download_manager.get_downloads_by_status(
            DownloadStatus.PENDING)
        for download in pending_downloads:
            # Find download_id for this download
            for download_id, item in self.download_manager.downloads.items():
                if item == download:
                    self.download_manager.start_download(download_id)
                    break

    def pause_all_downloads(self):
        """Pause all active downloads"""
        active_downloads = self.download_manager.get_downloads_by_status(
            DownloadStatus.DOWNLOADING)
        for download in active_downloads:
            # Find download_id for this download
            for download_id, item in self.download_manager.downloads.items():
                if item == download:
                    self.download_manager.pause_download(download_id)
                    break

    def clear_completed_downloads(self):
        """Clear completed downloads"""
        completed_downloads = self.download_manager.get_downloads_by_status(
            DownloadStatus.COMPLETED)
        for download in completed_downloads:
            # Find download_id for this download
            for download_id, item in self.download_manager.downloads.items():
                if item == download:
                    self.download_manager.remove_download(download_id)
                    break
        self.refresh_download_list()

    def open_downloads_folder(self):
        """Open downloads folder"""
        self.open_folder(self.download_manager.download_dir)

    def open_file(self, filepath: str):
        """Open file with default application"""
        import subprocess
        import platform

        try:
            if platform.system() == 'Darwin':  # macOS
                subprocess.call(['open', filepath])
            elif platform.system() == 'Windows':  # Windows
                os.startfile(filepath)
            else:  # Linux
                subprocess.call(['xdg-open', filepath])
        except Exception as e:
            QMessageBox.warning(self, 'Error', f'Could not open file: {e}')

    def open_folder(self, folder_path: str):
        """Open folder in file manager"""
        import subprocess
        import platform

        try:
            if platform.system() == 'Darwin':  # macOS
                subprocess.call(['open', folder_path])
            elif platform.system() == 'Windows':  # Windows
                os.startfile(folder_path)
            else:  # Linux
                subprocess.call(['xdg-open', folder_path])
        except Exception as e:
            QMessageBox.warning(self, 'Error', f'Could not open folder: {e}')

    def show_about(self):
        """Show about dialog"""
        QMessageBox.about(self, 'About IDM Clone',
                          'IDM Clone v1.0\n\n'
                          'A simple download manager with IDM-style features.\n'
                          'Built with Python and PyQt6.')

    def on_download_progress(self, download_id: str, progress_info: ProgressInfo):
        """Handle download progress updates"""
        # Update will be handled by the timer
        pass

    def on_download_status_changed(self, download_id: str, status: DownloadStatus):
        """Handle download status changes"""
        download_item = self.download_manager.get_download(download_id)
        if download_item:
            self.status_bar.showMessage(
                f'{download_item.filename}: {status.value}')

    def update_ui(self):
        """Update UI periodically"""
        # Update download list
        self.download_list.update_progress()

        # Update status bar with stats
        stats = self.download_manager.get_stats()
        active = stats['active_downloads']
        total = stats['total_downloads']

        if active > 0:
            self.status_bar.showMessage(
                f'Active downloads: {active} | Total: {total}')
        else:
            self.status_bar.showMessage(f'Ready | Total downloads: {total}')

    def closeEvent(self, event):
        """Handle application close"""
        # Save state
        self.download_manager.save_state()

        # Check for active downloads
        active_downloads = self.download_manager.get_downloads_by_status(
            DownloadStatus.DOWNLOADING)
        if active_downloads:
            reply = QMessageBox.question(self, 'Active Downloads',
                                         f'There are {len(active_downloads)} active downloads. '
                                         'Do you want to exit anyway?',
                                         QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No)
            if reply == QMessageBox.StandardButton.No:
                event.ignore()
                return

        # Shutdown download manager
        self.download_manager.shutdown()
        event.accept()
