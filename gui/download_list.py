"""
Download list widget for displaying downloads in a table
"""

from PyQt6.QtWidgets import (QWidget, QVBoxLayout, QTreeWidget, QTreeWidgetItem,
                             QHeaderView, QMenu, QProgressBar, QHBoxLayout, QLabel)
from PyQt6.QtCore import Qt, pyqtSignal
from PyQt6.QtGui import QAction, QColor, QBrush
from typing import List, Dict, Optional

from core.download_item import DownloadItem
from core.download_engine import DownloadStatus


class ProgressWidget(QWidget):
    """Custom progress widget for download list"""

    def __init__(self, progress_percent: float = 0.0):
        super().__init__()
        self.setup_ui()
        self.set_progress(progress_percent)

    def setup_ui(self):
        layout = QHBoxLayout()
        layout.setContentsMargins(2, 2, 2, 2)

        self.progress_bar = QProgressBar()
        self.progress_bar.setMinimum(0)
        self.progress_bar.setMaximum(100)
        self.progress_bar.setTextVisible(True)

        layout.addWidget(self.progress_bar)
        self.setLayout(layout)

    def set_progress(self, percent: float):
        self.progress_bar.setValue(int(percent))
        self.progress_bar.setFormat(f"{percent:.1f}%")


class DownloadListWidget(QWidget):
    """
    Widget for displaying downloads in a table format similar to IDM
    """

    download_action_requested = pyqtSignal(str, str)  # action, download_id

    def __init__(self):
        super().__init__()
        self.download_items: Dict[str, DownloadItem] = {}
        self.download_ids: List[str] = []
        self.setup_ui()

    def setup_ui(self):
        """Setup the user interface"""
        layout = QVBoxLayout()
        layout.setContentsMargins(0, 0, 0, 0)

        # Create tree widget
        self.tree = QTreeWidget()
        self.tree.setRootIsDecorated(False)
        self.tree.setAlternatingRowColors(True)
        self.tree.setSelectionBehavior(
            QTreeWidget.SelectionBehavior.SelectRows)
        self.tree.setContextMenuPolicy(Qt.ContextMenuPolicy.CustomContextMenu)

        # Setup columns
        columns = ['Filename', 'Size', 'Progress',
                   'Speed', 'Status', 'Time Left', 'Segments']
        self.tree.setHeaderLabels(columns)

        # Configure column widths
        header = self.tree.header()
        header.setSectionResizeMode(
            0, QHeaderView.ResizeMode.Stretch)  # Filename
        header.setSectionResizeMode(
            1, QHeaderView.ResizeMode.ResizeToContents)  # Size
        header.setSectionResizeMode(
            2, QHeaderView.ResizeMode.Fixed)  # Progress
        header.setSectionResizeMode(
            3, QHeaderView.ResizeMode.ResizeToContents)  # Speed
        header.setSectionResizeMode(
            4, QHeaderView.ResizeMode.ResizeToContents)  # Status
        header.setSectionResizeMode(
            5, QHeaderView.ResizeMode.ResizeToContents)  # Time Left
        header.setSectionResizeMode(
            6, QHeaderView.ResizeMode.ResizeToContents)  # Segments

        # Set fixed width for progress column
        self.tree.setColumnWidth(2, 150)

        # Connect signals
        self.tree.customContextMenuRequested.connect(self.show_context_menu)
        self.tree.itemDoubleClicked.connect(self.on_item_double_clicked)

        layout.addWidget(self.tree)
        self.setLayout(layout)

    def update_downloads(self, downloads: List[DownloadItem], download_ids: List[str]):
        """Update the download list"""
        self.download_items = {
            download_ids[i]: downloads[i] for i in range(len(downloads))}
        self.download_ids = download_ids

        # Clear existing items
        self.tree.clear()

        # Add download items
        for i, download_id in enumerate(download_ids):
            download_item = downloads[i]
            self.add_download_item(download_id, download_item)

    def add_download_item(self, download_id: str, download_item: DownloadItem):
        """Add a download item to the tree"""
        item = QTreeWidgetItem()

        # Set data
        item.setData(0, Qt.ItemDataRole.UserRole, download_id)

        # Update item display
        self.update_download_item(item, download_item)

        # Add to tree
        self.tree.addTopLevelItem(item)

        # Create and set progress widget
        progress_widget = ProgressWidget(download_item.progress_percent)
        self.tree.setItemWidget(item, 2, progress_widget)

    def update_download_item(self, item: QTreeWidgetItem, download_item: DownloadItem):
        """Update a download item's display"""
        # Filename
        item.setText(0, download_item.filename)

        # Size
        item.setText(1, download_item.file_size_str)

        # Progress is handled by widget

        # Speed
        if download_item.status == DownloadStatus.DOWNLOADING:
            item.setText(3, download_item.speed_str)
        else:
            item.setText(3, "")

        # Status
        item.setText(4, download_item.status_str)

        # Time Left
        if download_item.status == DownloadStatus.DOWNLOADING and download_item.eta_seconds:
            item.setText(5, download_item.eta_str)
        else:
            item.setText(5, "")

        # Segments
        if download_item.total_segments > 1:
            item.setText(6, download_item.segments_str)
        else:
            item.setText(6, "")

        # Set row color based on status
        self.set_item_color(item, download_item.status)

    def set_item_color(self, item: QTreeWidgetItem, status: DownloadStatus):
        """Set item color based on status"""
        if status == DownloadStatus.COMPLETED:
            color = QColor(144, 238, 144)  # Light green
        elif status == DownloadStatus.DOWNLOADING:
            color = QColor(173, 216, 230)  # Light blue
        elif status == DownloadStatus.FAILED:
            color = QColor(255, 182, 193)  # Light red
        elif status == DownloadStatus.PAUSED:
            color = QColor(255, 255, 224)  # Light yellow
        elif status == DownloadStatus.CANCELLED:
            color = QColor(211, 211, 211)  # Light gray
        else:
            color = QColor(255, 255, 255)  # White

        brush = QBrush(color)
        for col in range(self.tree.columnCount()):
            item.setBackground(col, brush)

    def update_progress(self):
        """Update progress for all items"""
        for i in range(self.tree.topLevelItemCount()):
            item = self.tree.topLevelItem(i)
            download_id = item.data(0, Qt.ItemDataRole.UserRole)

            if download_id in self.download_items:
                download_item = self.download_items[download_id]

                # Update item text
                self.update_download_item(item, download_item)

                # Update progress widget
                progress_widget = self.tree.itemWidget(item, 2)
                if isinstance(progress_widget, ProgressWidget):
                    progress_widget.set_progress(
                        download_item.progress_percent)

    def get_selected_download_id(self) -> Optional[str]:
        """Get the selected download ID"""
        current_item = self.tree.currentItem()
        if current_item:
            return current_item.data(0, Qt.ItemDataRole.UserRole)
        return None

    def show_context_menu(self, position):
        """Show context menu"""
        item = self.tree.itemAt(position)
        if not item:
            return

        download_id = item.data(0, Qt.ItemDataRole.UserRole)
        if download_id not in self.download_items:
            return

        download_item = self.download_items[download_id]

        menu = QMenu(self)

        # Add actions based on status
        if download_item.status == DownloadStatus.PENDING:
            start_action = QAction('Start Download', self)
            start_action.triggered.connect(
                lambda: self.download_action_requested.emit('start', download_id))
            menu.addAction(start_action)

        elif download_item.status == DownloadStatus.DOWNLOADING:
            pause_action = QAction('Pause Download', self)
            pause_action.triggered.connect(
                lambda: self.download_action_requested.emit('pause', download_id))
            menu.addAction(pause_action)

            cancel_action = QAction('Cancel Download', self)
            cancel_action.triggered.connect(
                lambda: self.download_action_requested.emit('cancel', download_id))
            menu.addAction(cancel_action)

        elif download_item.status == DownloadStatus.PAUSED:
            resume_action = QAction('Resume Download', self)
            resume_action.triggered.connect(
                lambda: self.download_action_requested.emit('resume', download_id))
            menu.addAction(resume_action)

            cancel_action = QAction('Cancel Download', self)
            cancel_action.triggered.connect(
                lambda: self.download_action_requested.emit('cancel', download_id))
            menu.addAction(cancel_action)

        elif download_item.status == DownloadStatus.FAILED:
            if download_item.can_retry():
                retry_action = QAction('Retry Download', self)
                retry_action.triggered.connect(
                    lambda: self.download_action_requested.emit('retry', download_id))
                menu.addAction(retry_action)

        elif download_item.status == DownloadStatus.COMPLETED:
            open_file_action = QAction('Open File', self)
            open_file_action.triggered.connect(
                lambda: self.download_action_requested.emit('open_file', download_id))
            menu.addAction(open_file_action)

        # Common actions
        if download_item.status in [DownloadStatus.COMPLETED, DownloadStatus.FAILED, DownloadStatus.CANCELLED]:
            menu.addSeparator()

        open_folder_action = QAction('Open Folder', self)
        open_folder_action.triggered.connect(
            lambda: self.download_action_requested.emit('open_folder', download_id))
        menu.addAction(open_folder_action)

        menu.addSeparator()

        remove_action = QAction('Remove from List', self)
        remove_action.triggered.connect(
            lambda: self.download_action_requested.emit('remove', download_id))
        menu.addAction(remove_action)

        # Show menu
        menu.exec(self.tree.mapToGlobal(position))

    def on_item_double_clicked(self, item: QTreeWidgetItem, column: int):
        """Handle item double click"""
        download_id = item.data(0, Qt.ItemDataRole.UserRole)
        if download_id in self.download_items:
            download_item = self.download_items[download_id]

            if download_item.status == DownloadStatus.COMPLETED:
                self.download_action_requested.emit('open_file', download_id)
            elif download_item.status == DownloadStatus.PENDING:
                self.download_action_requested.emit('start', download_id)
            elif download_item.status == DownloadStatus.PAUSED:
                self.download_action_requested.emit('resume', download_id)
